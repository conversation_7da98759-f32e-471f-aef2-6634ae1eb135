@using Umbraco.Cms.Web.Common.PublishedModels;
@using MddPlus.Helpers;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = null;
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
    var isRtl = TranslationHelper.IsRTL(Context);
    var isArabic = currentLanguage == "ar";
}
<!DOCTYPE html>
<html lang="@currentLanguage" dir="@(isRtl ? "rtl" : "ltr")">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    
    <!-- SEO Meta Tags -->
    <title>@(ViewBag.Title ?? Model.Name) - مدد بلس</title>
    <meta name="description" content="@(ViewBag.Description ?? "منصة التمويل الجماعي المتوافقة مع الشريعة الإسلامية")">
    <meta name="keywords" content="@(ViewBag.Keywords ?? "تمويل جماعي، شريعة إسلامية، استثمار، مدد بلس")">
    <meta name="author" content="مدد بلس">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@(ViewBag.Title ?? Model.Name)">
    <meta property="og:description" content="@(ViewBag.Description ?? "منصة التمويل الجماعي المتوافقة مع الشريعة الإسلامية")">
    <meta property="og:type" content="website">
    <meta property="og:url" content="@Context.Request.GetDisplayUrl()">
    <meta property="og:locale" content="@(isArabic ? "ar_SA" : "en_US")">
    <meta property="og:site_name" content="مدد بلس">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@(ViewBag.Title ?? Model.Name)">
    <meta name="twitter:description" content="@(ViewBag.Description ?? "منصة التمويل الجماعي المتوافقة مع الشريعة الإسلامية")">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        'mdd-primary': '#005B82',
                        'mdd-secondary': '#007BA7',
                        'mdd-accent': '#BCBEC0',
                        'mdd-text': '#3C3C3B',
                        'mdd-dark-bg': '#1F2937',
                        'mdd-dark-accent': '#33B5E5'
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/advanced-features.css">
    @if (isRtl)
    {
        <link rel="stylesheet" href="/css/rtl.css">
    }
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#005B82">
    <meta name="msapplication-TileColor" content="#005B82">
    
    @RenderSection("Head", required: false)
</head>

<body class="font-@(isArabic ? "arabic" : "english") bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300" dir="@(isRtl ? "rtl" : "ltr")">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-mdd-primary text-white px-4 py-2 rounded-md z-50">
        @(isArabic ? "انتقل إلى المحتوى الرئيسي" : "Skip to main content")
    </a>
    
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40 transition-all duration-300">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16 lg:h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-mdd-primary to-mdd-secondary rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg lg:text-xl">م</span>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">مدد بلس</h1>
                            <p class="text-xs lg:text-sm text-gray-500 dark:text-gray-400">@(isArabic ? "منصة التمويل الجماعي" : "Crowdfunding Platform")</p>
                        </div>
                    </a>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden lg:flex items-center space-x-8 space-x-reverse">
                    <a href="/" class="nav-link @(Model.ContentType.Alias == "homePageModel" ? "active" : "")">
                        @(isArabic ? "الرئيسية" : "Home")
                    </a>
                    <a href="/services" class="nav-link @(Model.ContentType.Alias == "servicesPageModel" ? "active" : "")">
                        @(isArabic ? "خدماتنا" : "Services")
                    </a>
                    <a href="/about" class="nav-link">
                        @(isArabic ? "من نحن" : "About")
                    </a>
                    <a href="/contact" class="nav-link">
                        @(isArabic ? "اتصل بنا" : "Contact")
                    </a>
                </nav>
                
                <!-- Header Actions -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button id="language-toggle" class="flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <span class="text-sm font-medium">@(isArabic ? "العربية" : "English")</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="language-menu" class="language-menu hidden">
                            <a href="?culture=ar-SA" class="language-option @(isArabic ? "active" : "")">
                                <span class="flag">🇸🇦</span>
                                <span>العربية</span>
                            </a>
                            <a href="?culture=en-US" class="language-option @(!isArabic ? "active" : "")">
                                <span class="flag">🇺🇸</span>
                                <span>English</span>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <svg class="w-5 h-5 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    
                    <!-- Mobile Menu Toggle -->
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-4 space-y-2">
                <a href="/" class="mobile-nav-link @(Model.ContentType.Alias == "homePageModel" ? "active" : "")">
                    @(isArabic ? "الرئيسية" : "Home")
                </a>
                <a href="/services" class="mobile-nav-link @(Model.ContentType.Alias == "servicesPageModel" ? "active" : "")">
                    @(isArabic ? "خدماتنا" : "Services")
                </a>
                <a href="/about" class="mobile-nav-link">
                    @(isArabic ? "من نحن" : "About")
                </a>
                <a href="/contact" class="mobile-nav-link">
                    @(isArabic ? "اتصل بنا" : "Contact")
                </a>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @RenderBody()
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 dark:bg-gray-950 text-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 space-x-reverse mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-mdd-primary to-mdd-secondary rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xl">م</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">مدد بلس</h3>
                            <p class="text-gray-400 text-sm">@(isArabic ? "منصة التمويل الجماعي المتوافقة مع الشريعة" : "Sharia-Compliant Crowdfunding Platform")</p>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6">
                        @(isArabic ? "شركة سعودية مرخصة متخصصة في التمويل الجماعي القائم على الدين المتوافق مع الشريعة الإسلامية. نربط المستثمرين والمقترضين من خلال حلول مالية موثوقة ومبتكرة." : "Licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding. Connecting investors and borrowers through reliable and innovative financial solutions.")
                    </p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">@(isArabic ? "روابط سريعة" : "Quick Links")</h4>
                    <ul class="space-y-2">
                        <li><a href="/" class="footer-link">@(isArabic ? "الرئيسية" : "Home")</a></li>
                        <li><a href="/services" class="footer-link">@(isArabic ? "خدماتنا" : "Services")</a></li>
                        <li><a href="/about" class="footer-link">@(isArabic ? "من نحن" : "About")</a></li>
                        <li><a href="/contact" class="footer-link">@(isArabic ? "اتصل بنا" : "Contact")</a></li>
                        <li><a href="/investors" class="footer-link">@(isArabic ? "للمستثمرين" : "For Investors")</a></li>
                        <li><a href="/borrowers" class="footer-link">@(isArabic ? "للمقترضين" : "For Borrowers")</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">@(isArabic ? "معلومات التواصل" : "Contact Info")</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center space-x-3 space-x-reverse">
                            <svg class="w-4 h-4 text-mdd-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-gray-300"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3 space-x-reverse">
                            <svg class="w-4 h-4 text-mdd-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span class="text-gray-300">+966 11 123 4567</span>
                        </li>
                        <li class="flex items-center space-x-3 space-x-reverse">
                            <svg class="w-4 h-4 text-mdd-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span class="text-gray-300">@(isArabic ? "الرياض، المملكة العربية السعودية" : "Riyadh, Saudi Arabia")</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    © @DateTime.Now.Year @(isArabic ? "مدد بلس. جميع الحقوق محفوظة." : "MDD Plus. All rights reserved.")
                </p>
                <div class="flex space-x-6 space-x-reverse mt-4 md:mt-0">
                    <a href="/privacy" class="footer-link text-sm">@(isArabic ? "سياسة الخصوصية" : "Privacy Policy")</a>
                    <a href="/terms" class="footer-link text-sm">@(isArabic ? "شروط الاستخدام" : "Terms of Service")</a>
                    <a href="/cookies" class="footer-link text-sm">@(isArabic ? "سياسة الكوكيز" : "Cookie Policy")</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="/js/theme-toggle.js"></script>
    <script src="/js/advanced-language-switcher.js"></script>
    <script src="/js/advanced-features.js"></script>
    <script src="/js/main.js"></script>
    
    @RenderSection("Scripts", required: false)
    
    <!-- Analytics -->
    @if (!string.IsNullOrEmpty(ViewBag.GoogleAnalyticsId))
    {
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=@ViewBag.GoogleAnalyticsId"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '@ViewBag.GoogleAnalyticsId');
        </script>
    }
</body>
</html>
