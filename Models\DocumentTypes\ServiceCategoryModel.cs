using Umbraco.Cms.Core.Models.PublishedContent;
using System.Collections.Generic;
using System.Linq;

namespace MddPlus.Models.DocumentTypes
{
    public class ServiceCategoryModel : PublishedContentModel
    {
        public ServiceCategoryModel(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        public string TitleAr => this.Value<string>("titleAr") ?? "";
        public string TitleEn => this.Value<string>("titleEn") ?? "";
        public string DescriptionAr => this.Value<string>("descriptionAr") ?? "";
        public string DescriptionEn => this.Value<string>("descriptionEn") ?? "";
        public string Icon => this.Value<string>("icon") ?? "🏢";
        public string Color => this.Value<string>("color") ?? "blue";
        public string MaxAmount => this.Value<string>("maxAmount") ?? "";
        public string PaymentPeriod => this.Value<string>("paymentPeriod") ?? "";
        public string InterestRate => this.Value<string>("interestRate") ?? "";
        public IEnumerable<ServiceFeatureModel> Features => this.Value<IEnumerable<IPublishedContent>>("features")?.OfType<ServiceFeatureModel>() ?? Enumerable.Empty<ServiceFeatureModel>();
        public string LinkUrl => this.Value<string>("linkUrl") ?? "";
        public bool IsPopular => this.Value<bool>("isPopular");
        public new int SortOrder => this.Value<int>("sortOrder");

        public string GetTitle(string language = "ar")
        {
            return language == "ar" ? TitleAr : TitleEn;
        }

        public string GetDescription(string language = "ar")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }

        public string GetGradientClass()
        {
            return Color switch
            {
                "blue" => "from-blue-500 to-blue-600",
                "green" => "from-green-500 to-green-600",
                "purple" => "from-purple-500 to-purple-600",
                "orange" => "from-orange-500 to-orange-600",
                "red" => "from-red-500 to-red-600",
                "indigo" => "from-indigo-500 to-indigo-600",
                "yellow" => "from-yellow-500 to-yellow-600",
                "pink" => "from-pink-500 to-pink-600",
                _ => "from-blue-500 to-blue-600"
            };
        }

        public string GetColorClass()
        {
            return Color switch
            {
                "blue" => "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
                "green" => "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
                "purple" => "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200",
                "orange" => "bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200",
                "red" => "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
                "indigo" => "bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200",
                _ => "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
            };
        }
    }
}
