using System.ComponentModel.DataAnnotations;

namespace MddPlus.Models.Admin
{
    public class AdminDashboardModel
    {
        public int TotalPages { get; set; }
        public int TotalStatistics { get; set; }
        public int TotalFeatures { get; set; }
        public int TotalServiceCategories { get; set; }
        public List<ActivityLogModel> RecentActivities { get; set; } = new();
        public SystemStatusModel SystemStatus { get; set; } = new();
    }

    public class ActivityLogModel
    {
        public int Id { get; set; }
        public string Action { get; set; } = "";
        public string Description { get; set; } = "";
        public string UserName { get; set; } = "";
        public DateTime CreatedAt { get; set; }
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "blue";
    }

    public class SystemStatusModel
    {
        public bool IsOnline { get; set; } = true;
        public string Version { get; set; } = "1.0.0";
        public DateTime LastBackup { get; set; }
        public long DatabaseSize { get; set; }
        public int ActiveUsers { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
    }

    public class ContentManagementModel
    {
        public List<PageSummaryModel> Pages { get; set; } = new();
        public List<string> Languages { get; set; } = new();
    }

    public class PageSummaryModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string Status { get; set; } = "";
        public DateTime LastModified { get; set; }
        public string ModifiedBy { get; set; } = "";
        public bool IsPublished { get; set; }
        public string Url { get; set; } = "";
    }

    public class HomePageContentModel
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "العنوان العربي مطلوب")]
        public string TitleAr { get; set; } = "";
        
        [Required(ErrorMessage = "العنوان الإنجليزي مطلوب")]
        public string TitleEn { get; set; } = "";
        
        public string DescriptionAr { get; set; } = "";
        public string DescriptionEn { get; set; } = "";
        
        [Required(ErrorMessage = "عنوان البطل العربي مطلوب")]
        public string HeroTitleAr { get; set; } = "";
        
        [Required(ErrorMessage = "عنوان البطل الإنجليزي مطلوب")]
        public string HeroTitleEn { get; set; } = "";
        
        public string HeroSubtitleAr { get; set; } = "";
        public string HeroSubtitleEn { get; set; } = "";
        
        public string CtaTitleAr { get; set; } = "";
        public string CtaTitleEn { get; set; } = "";
        public string CtaSubtitleAr { get; set; } = "";
        public string CtaSubtitleEn { get; set; } = "";
        public string CtaButton1TextAr { get; set; } = "";
        public string CtaButton1TextEn { get; set; } = "";
        public string CtaButton1Link { get; set; } = "";
        public string CtaButton2TextAr { get; set; } = "";
        public string CtaButton2TextEn { get; set; } = "";
        public string CtaButton2Link { get; set; } = "";
        
        public List<StatisticItemContentModel> Statistics { get; set; } = new();
        public List<FeatureItemContentModel> Features { get; set; } = new();
    }

    public class StatisticItemContentModel
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "الرقم مطلوب")]
        public string Number { get; set; } = "";
        
        public string Unit { get; set; } = "";
        
        [Required(ErrorMessage = "التسمية العربية مطلوبة")]
        public string LabelAr { get; set; } = "";
        
        [Required(ErrorMessage = "التسمية الإنجليزية مطلوبة")]
        public string LabelEn { get; set; } = "";
        
        public string DescriptionAr { get; set; } = "";
        public string DescriptionEn { get; set; } = "";
        
        [Required(ErrorMessage = "اللون مطلوب")]
        public string Color { get; set; } = "blue";
        
        public string Icon { get; set; } = "📊";
        
        [Range(0, 5000, ErrorMessage = "تأخير الأنيميشن يجب أن يكون بين 0 و 5000")]
        public int AnimationDelay { get; set; }
        
        [Range(0, 100, ErrorMessage = "نسبة التقدم يجب أن تكون بين 0 و 100")]
        public int ProgressPercentage { get; set; }
        
        [Range(0, 100, ErrorMessage = "ترتيب العرض يجب أن يكون بين 0 و 100")]
        public int SortOrder { get; set; }
    }

    public class FeatureItemContentModel
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "العنوان العربي مطلوب")]
        public string TitleAr { get; set; } = "";
        
        [Required(ErrorMessage = "العنوان الإنجليزي مطلوب")]
        public string TitleEn { get; set; } = "";
        
        [Required(ErrorMessage = "الوصف العربي مطلوب")]
        public string DescriptionAr { get; set; } = "";
        
        [Required(ErrorMessage = "الوصف الإنجليزي مطلوب")]
        public string DescriptionEn { get; set; } = "";
        
        public string Icon { get; set; } = "🔧";
        
        [Required(ErrorMessage = "اللون مطلوب")]
        public string Color { get; set; } = "blue";
        
        public string LinkUrl { get; set; } = "";
        public string LinkTextAr { get; set; } = "";
        public string LinkTextEn { get; set; } = "";
        public bool IsHighlighted { get; set; }
        
        [Range(0, 100, ErrorMessage = "ترتيب العرض يجب أن يكون بين 0 و 100")]
        public int SortOrder { get; set; }
    }

    public class ServicesPageContentModel
    {
        public int Id { get; set; }
        public string TitleAr { get; set; } = "";
        public string TitleEn { get; set; } = "";
        public string DescriptionAr { get; set; } = "";
        public string DescriptionEn { get; set; } = "";
        public string HeroTitleAr { get; set; } = "";
        public string HeroTitleEn { get; set; } = "";
        public string HeroSubtitleAr { get; set; } = "";
        public string HeroSubtitleEn { get; set; } = "";
        
        public List<ServiceCategoryContentModel> ServiceCategories { get; set; } = new();
        public List<ProcessStepContentModel> ProcessSteps { get; set; } = new();
    }

    public class ServiceCategoryContentModel
    {
        public int Id { get; set; }
        public string TitleAr { get; set; } = "";
        public string TitleEn { get; set; } = "";
        public string DescriptionAr { get; set; } = "";
        public string DescriptionEn { get; set; } = "";
        public string Icon { get; set; } = "🏢";
        public string Color { get; set; } = "blue";
        public string MaxAmount { get; set; } = "";
        public string PaymentPeriod { get; set; } = "";
        public string InterestRate { get; set; } = "";
        public string LinkUrl { get; set; } = "";
        public bool IsPopular { get; set; }
        public int SortOrder { get; set; }
        
        public List<ServiceFeatureContentModel> Features { get; set; } = new();
    }

    public class ServiceFeatureContentModel
    {
        public int Id { get; set; }
        public string TextAr { get; set; } = "";
        public string TextEn { get; set; } = "";
        public string Icon { get; set; } = "✓";
        public string Color { get; set; } = "green";
        public bool IsHighlighted { get; set; }
        public int SortOrder { get; set; }
    }

    public class ProcessStepContentModel
    {
        public int Id { get; set; }
        public int StepNumber { get; set; }
        public string TitleAr { get; set; } = "";
        public string TitleEn { get; set; } = "";
        public string DescriptionAr { get; set; } = "";
        public string DescriptionEn { get; set; } = "";
        public string Icon { get; set; } = "🔧";
        public string Color { get; set; } = "blue";
        public int SortOrder { get; set; }
    }

    public class MediaFileModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Url { get; set; } = "";
        public string Type { get; set; } = "";
        public long Size { get; set; }
        public DateTime UploadedAt { get; set; }
        public string UploadedBy { get; set; } = "";
        public string Alt { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class SiteSettingsModel
    {
        public string SiteName { get; set; } = "";
        public string SiteDescription { get; set; } = "";
        public string DefaultLanguage { get; set; } = "ar";
        public bool EnableMultiLanguage { get; set; } = true;
        public string ContactEmail { get; set; } = "";
        public string ContactPhone { get; set; } = "";
        public string Address { get; set; } = "";
        public string FacebookUrl { get; set; } = "";
        public string TwitterUrl { get; set; } = "";
        public string LinkedInUrl { get; set; } = "";
        public string InstagramUrl { get; set; } = "";
        public bool EnableAnalytics { get; set; } = true;
        public string GoogleAnalyticsId { get; set; } = "";
        public bool EnableCookieConsent { get; set; } = true;
        public string ThemeColor { get; set; } = "#005B82";
        public string LogoUrl { get; set; } = "";
        public string FaviconUrl { get; set; } = "";
    }
}
