// Admin Panel JavaScript for MDD Plus

class AdminPanel {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupSidebar();
        this.setupThemeToggle();
        this.setupToasts();
        this.setupFormHandlers();
        this.setupLoadingOverlay();
    }
    
    // Sidebar Management
    setupSidebar() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const closeSidebar = document.getElementById('close-sidebar');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        if (closeSidebar) {
            closeSidebar.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // Close sidebar on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
            }
        });
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        if (sidebar && overlay) {
            const isOpen = !sidebar.classList.contains('translate-x-full');
            
            if (isOpen) {
                this.closeSidebar();
            } else {
                this.openSidebar();
            }
        }
    }
    
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }
    
    // Theme Toggle
    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('admin-theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
        }
    }
    
    toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        
        if (isDark) {
            document.documentElement.classList.remove('dark');
            localStorage.setItem('admin-theme', 'light');
        } else {
            document.documentElement.classList.add('dark');
            localStorage.setItem('admin-theme', 'dark');
        }
    }
    
    // Toast Notifications
    setupToasts() {
        this.successToast = document.getElementById('success-toast');
        this.errorToast = document.getElementById('error-toast');
        this.successMessage = document.getElementById('success-message');
        this.errorMessage = document.getElementById('error-message');
    }
    
    showSuccessToast(message) {
        if (this.successToast && this.successMessage) {
            this.successMessage.textContent = message;
            this.successToast.classList.remove('translate-x-full');
            
            setTimeout(() => {
                this.successToast.classList.add('translate-x-full');
            }, 3000);
        }
    }
    
    showErrorToast(message) {
        if (this.errorToast && this.errorMessage) {
            this.errorMessage.textContent = message;
            this.errorToast.classList.remove('translate-x-full');
            
            setTimeout(() => {
                this.errorToast.classList.add('translate-x-full');
            }, 5000);
        }
    }
    
    // Loading Overlay
    setupLoadingOverlay() {
        this.loadingOverlay = document.getElementById('loading-overlay');
    }
    
    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('hidden');
        }
    }
    
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('hidden');
        }
    }
    
    // Form Handlers
    setupFormHandlers() {
        // Auto-save functionality
        const forms = document.querySelectorAll('form[data-auto-save]');
        forms.forEach(form => {
            this.setupAutoSave(form);
        });
        
        // Form validation
        const requiredInputs = document.querySelectorAll('input[required], textarea[required]');
        requiredInputs.forEach(input => {
            this.setupValidation(input);
        });
    }
    
    setupAutoSave(form) {
        let timeout;
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.autoSaveForm(form);
                }, 2000);
            });
        });
    }
    
    async autoSaveForm(form) {
        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch(form.action || window.location.pathname, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                this.showAutoSaveIndicator();
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
    
    showAutoSaveIndicator() {
        // Show a subtle indicator that content was auto-saved
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm z-50';
        indicator.textContent = 'تم الحفظ التلقائي';
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.remove();
        }, 2000);
    }
    
    setupValidation(input) {
        input.addEventListener('blur', () => {
            this.validateInput(input);
        });
        
        input.addEventListener('input', () => {
            if (input.classList.contains('error')) {
                this.validateInput(input);
            }
        });
    }
    
    validateInput(input) {
        const isValid = input.checkValidity();
        const errorElement = input.parentElement.querySelector('.admin-form-error');
        
        if (!isValid) {
            input.classList.add('border-red-500', 'error');
            input.classList.remove('border-gray-300');
            
            if (!errorElement) {
                const error = document.createElement('p');
                error.className = 'admin-form-error mt-1';
                error.textContent = input.validationMessage || 'هذا الحقل مطلوب';
                input.parentElement.appendChild(error);
            }
        } else {
            input.classList.remove('border-red-500', 'error');
            input.classList.add('border-gray-300');
            
            if (errorElement) {
                errorElement.remove();
            }
        }
        
        return isValid;
    }
    
    // API Helpers
    async apiRequest(url, options = {}) {
        this.showLoading();
        
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            const data = await response.json();
            
            if (response.ok) {
                if (data.message) {
                    this.showSuccessToast(data.message);
                }
                return data;
            } else {
                throw new Error(data.message || 'حدث خطأ في الخادم');
            }
        } catch (error) {
            this.showErrorToast(error.message || 'حدث خطأ غير متوقع');
            throw error;
        } finally {
            this.hideLoading();
        }
    }
    
    // Utility Methods
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Drag and Drop Helpers
    setupSortable(container, options = {}) {
        if (!container) return;
        
        let draggedElement = null;
        let placeholder = null;
        
        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('sortable-item')) {
                draggedElement = e.target;
                e.target.classList.add('dragging');
                
                // Create placeholder
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.style.height = e.target.offsetHeight + 'px';
            }
        });
        
        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('sortable-item')) {
                e.target.classList.remove('dragging');
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedElement = null;
                placeholder = null;
                
                if (options.onSort) {
                    options.onSort();
                }
            }
        });
        
        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            
            if (draggedElement && placeholder) {
                const afterElement = this.getDragAfterElement(container, e.clientY);
                
                if (afterElement == null) {
                    container.appendChild(placeholder);
                } else {
                    container.insertBefore(placeholder, afterElement);
                }
            }
        });
        
        container.addEventListener('drop', (e) => {
            e.preventDefault();
            
            if (draggedElement && placeholder) {
                placeholder.parentNode.replaceChild(draggedElement, placeholder);
            }
        });
    }
    
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }
}

// Initialize Admin Panel
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminPanel;
}
