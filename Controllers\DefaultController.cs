using Microsoft.AspNetCore.Mvc;

namespace MddPlus.Controllers
{
    [Route("/")]
    public class DefaultController : Controller
    {
        [Route("")]
        [Route("home")]
        public IActionResult Index()
        {
            return View("~/Views/Index.cshtml");
        }

        [Route("about")]
        public IActionResult About()
        {
            return View("~/Views/About.cshtml");
        }

        [Route("services")]
        public IActionResult Services()
        {
            return View("~/Views/Services.cshtml");
        }

        [Route("test")]
        public IActionResult Test()
        {
            return View("~/Views/Test.cshtml");
        }

        [Route("en")]
        [Route("en/")]
        public IActionResult IndexEn()
        {
            return View("~/Views/IndexEn.cshtml");
        }
    }
}
