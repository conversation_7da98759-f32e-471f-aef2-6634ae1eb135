@using MddPlus.Models.Admin;
@model AdminDashboardModel
@{
    Layout = "_AdminLayout.cshtml";
    ViewBag.Title = "لوحة التحكم";
}

<!-- Dashboard Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">مرحباً بك في لوحة التحكم</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                إدارة محتوى موقع مدد بلس بسهولة وفعالية
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3 space-x-reverse">
            <button class="btn-secondary">
                📊 تقرير شامل
            </button>
            <button class="btn-primary">
                ➕ إضافة محتوى جديد
            </button>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="admin-grid-4 mb-8">
    <div class="stat-card">
        <div class="stat-card-icon bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
            📄
        </div>
        <div class="stat-card-value">@Model.TotalPages</div>
        <div class="stat-card-label">إجمالي الصفحات</div>
        <div class="stat-card-change positive">+2 هذا الشهر</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-icon bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
            📊
        </div>
        <div class="stat-card-value">@Model.TotalStatistics</div>
        <div class="stat-card-label">الإحصائيات</div>
        <div class="stat-card-change positive">+1 هذا الأسبوع</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-icon bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400">
            ⭐
        </div>
        <div class="stat-card-value">@Model.TotalFeatures</div>
        <div class="stat-card-label">المميزات</div>
        <div class="stat-card-change positive">+3 هذا الشهر</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-icon bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400">
            🏢
        </div>
        <div class="stat-card-value">@Model.TotalServiceCategories</div>
        <div class="stat-card-label">فئات الخدمات</div>
        <div class="stat-card-change">بدون تغيير</div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="admin-grid-3 gap-8">
    <!-- Recent Activities -->
    <div class="lg:col-span-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <div>
                    <h3 class="admin-card-title">النشاطات الأخيرة</h3>
                    <p class="admin-card-subtitle">آخر التحديثات والتغييرات</p>
                </div>
                <button class="btn-secondary text-sm">
                    عرض الكل
                </button>
            </div>
            
            <div class="space-y-4">
                @foreach (var activity in Model.RecentActivities)
                {
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-@(activity.Color)-100 dark:bg-@(activity.Color)-900 text-@(activity.Color)-600 dark:text-@(activity.Color)-400 rounded-full flex items-center justify-center text-sm">
                                @activity.Icon
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                @activity.Action
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                @activity.Description
                            </p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                @activity.CreatedAt.ToString("yyyy/MM/dd HH:mm") - @activity.UserName
                            </p>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div>
        <div class="admin-card">
            <div class="admin-card-header">
                <div>
                    <h3 class="admin-card-title">حالة النظام</h3>
                    <p class="admin-card-subtitle">معلومات النظام والأداء</p>
                </div>
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                    <span class="text-sm text-green-600 dark:text-green-400">متصل</span>
                </div>
            </div>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600 dark:text-gray-400">استخدام المعالج</span>
                        <span class="font-medium">@Model.SystemStatus.CpuUsage.ToString("F1")%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: @Model.SystemStatus.CpuUsage%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600 dark:text-gray-400">استخدام الذاكرة</span>
                        <span class="font-medium">@Model.SystemStatus.MemoryUsage.ToString("F1")%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: @Model.SystemStatus.MemoryUsage%"></div>
                    </div>
                </div>
                
                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الإصدار:</span>
                            <span class="font-medium">@Model.SystemStatus.Version</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">آخر نسخة احتياطية:</span>
                            <span class="font-medium">@Model.SystemStatus.LastBackup.ToString("yyyy/MM/dd")</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حجم قاعدة البيانات:</span>
                            <span class="font-medium">@((Model.SystemStatus.DatabaseSize / 1024 / 1024).ToString("F1")) ميجابايت</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">المستخدمون النشطون:</span>
                            <span class="font-medium">@Model.SystemStatus.ActiveUsers</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="admin-card mt-6">
            <div class="admin-card-header">
                <h3 class="admin-card-title">إجراءات سريعة</h3>
            </div>
            
            <div class="space-y-3">
                <a href="/admin/content/home" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg flex items-center justify-center text-sm ml-3">
                        🏠
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">تحرير الصفحة الرئيسية</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تحديث المحتوى والإحصائيات</p>
                    </div>
                </a>
                
                <a href="/admin/statistics" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-lg flex items-center justify-center text-sm ml-3">
                        📊
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">إدارة الإحصائيات</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">إضافة وتعديل الإحصائيات</p>
                    </div>
                </a>
                
                <a href="/admin/features" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 rounded-lg flex items-center justify-center text-sm ml-3">
                        ⭐
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">إدارة المميزات</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">إضافة وتعديل المميزات</p>
                    </div>
                </a>
                
                <a href="/admin/media" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded-lg flex items-center justify-center text-sm ml-3">
                        🖼️
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">إدارة الوسائط</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">رفع وإدارة الصور</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart -->
<div class="admin-card mt-8">
    <div class="admin-card-header">
        <div>
            <h3 class="admin-card-title">أداء الموقع</h3>
            <p class="admin-card-subtitle">إحصائيات الزيارات والأداء خلال آخر 7 أيام</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button class="btn-secondary text-sm">تصدير</button>
            <button class="btn-secondary text-sm">تحديث</button>
        </div>
    </div>
    
    <div class="h-64">
        <canvas id="performanceChart"></canvas>
    </div>
</div>

@section Scripts {
    <script>
        // Performance Chart
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                datasets: [{
                    label: 'الزيارات',
                    data: [120, 190, 300, 500, 200, 300, 450],
                    borderColor: '#005B82',
                    backgroundColor: 'rgba(0, 91, 130, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'مشاهدات الصفحة',
                    data: [200, 350, 450, 700, 350, 500, 650],
                    borderColor: '#33B5E5',
                    backgroundColor: 'rgba(51, 181, 229, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            // Update stats here
            console.log('Refreshing stats...');
        }, 30000);
        
        // Welcome animation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
}
