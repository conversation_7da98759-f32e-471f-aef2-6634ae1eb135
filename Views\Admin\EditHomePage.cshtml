@using MddPlus.Models.Admin;
@model HomePageContentModel
@{
    Layout = "_AdminLayout.cshtml";
    ViewBag.Title = "تحرير الصفحة الرئيسية";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">تحرير الصفحة الرئيسية</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                إدارة محتوى الصفحة الرئيسية والعناصر التفاعلية
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3 space-x-reverse">
            <a href="/admin/preview/home" target="_blank" class="btn-secondary">
                👁️ معاينة
            </a>
            <button type="submit" form="home-page-form" class="btn-primary">
                💾 حفظ التغييرات
            </button>
        </div>
    </div>
</div>

<!-- Form -->
<form id="home-page-form" class="space-y-8">
    <!-- Basic Information -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 class="admin-card-title">المعلومات الأساسية</h3>
            <p class="admin-card-subtitle">عنوان الصفحة ووصفها</p>
        </div>
        
        <div class="admin-grid-2">
            <div class="admin-form-group">
                <label class="admin-form-label" for="titleAr">العنوان (عربي) *</label>
                <input type="text" id="titleAr" name="titleAr" value="@Model.TitleAr" class="admin-form-input" required>
                <p class="admin-form-help">عنوان الصفحة الذي يظهر في المتصفح</p>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="titleEn">العنوان (إنجليزي) *</label>
                <input type="text" id="titleEn" name="titleEn" value="@Model.TitleEn" class="admin-form-input" required>
                <p class="admin-form-help">Page title that appears in browser</p>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="descriptionAr">الوصف (عربي)</label>
                <textarea id="descriptionAr" name="descriptionAr" rows="3" class="admin-form-textarea">@Model.DescriptionAr</textarea>
                <p class="admin-form-help">وصف الصفحة لمحركات البحث</p>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="descriptionEn">الوصف (إنجليزي)</label>
                <textarea id="descriptionEn" name="descriptionEn" rows="3" class="admin-form-textarea">@Model.DescriptionEn</textarea>
                <p class="admin-form-help">Page description for search engines</p>
            </div>
        </div>
    </div>
    
    <!-- Hero Section -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 class="admin-card-title">قسم البطل (Hero Section)</h3>
            <p class="admin-card-subtitle">المحتوى الرئيسي في أعلى الصفحة</p>
        </div>
        
        <div class="admin-grid-2">
            <div class="admin-form-group">
                <label class="admin-form-label" for="heroTitleAr">عنوان البطل (عربي) *</label>
                <input type="text" id="heroTitleAr" name="heroTitleAr" value="@Model.HeroTitleAr" class="admin-form-input" required>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="heroTitleEn">عنوان البطل (إنجليزي) *</label>
                <input type="text" id="heroTitleEn" name="heroTitleEn" value="@Model.HeroTitleEn" class="admin-form-input" required>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="heroSubtitleAr">العنوان الفرعي (عربي)</label>
                <textarea id="heroSubtitleAr" name="heroSubtitleAr" rows="3" class="admin-form-textarea">@Model.HeroSubtitleAr</textarea>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="heroSubtitleEn">العنوان الفرعي (إنجليزي)</label>
                <textarea id="heroSubtitleEn" name="heroSubtitleEn" rows="3" class="admin-form-textarea">@Model.HeroSubtitleEn</textarea>
            </div>
        </div>
    </div>
    
    <!-- Call to Action -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 class="admin-card-title">دعوة للعمل (Call to Action)</h3>
            <p class="admin-card-subtitle">الأزرار والروابط التفاعلية</p>
        </div>
        
        <div class="space-y-6">
            <div class="admin-grid-2">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="ctaTitleAr">عنوان الدعوة (عربي)</label>
                    <input type="text" id="ctaTitleAr" name="ctaTitleAr" value="@Model.CtaTitleAr" class="admin-form-input">
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label" for="ctaTitleEn">عنوان الدعوة (إنجليزي)</label>
                    <input type="text" id="ctaTitleEn" name="ctaTitleEn" value="@Model.CtaTitleEn" class="admin-form-input">
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label" for="ctaSubtitleAr">العنوان الفرعي (عربي)</label>
                    <textarea id="ctaSubtitleAr" name="ctaSubtitleAr" rows="2" class="admin-form-textarea">@Model.CtaSubtitleAr</textarea>
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label" for="ctaSubtitleEn">العنوان الفرعي (إنجليزي)</label>
                    <textarea id="ctaSubtitleEn" name="ctaSubtitleEn" rows="2" class="admin-form-textarea">@Model.CtaSubtitleEn</textarea>
                </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">الأزرار</h4>
                
                <div class="admin-grid-2 gap-6">
                    <!-- Button 1 -->
                    <div class="space-y-4">
                        <h5 class="font-medium text-gray-700 dark:text-gray-300">الزر الأول</h5>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton1TextAr">نص الزر (عربي)</label>
                            <input type="text" id="ctaButton1TextAr" name="ctaButton1TextAr" value="@Model.CtaButton1TextAr" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton1TextEn">نص الزر (إنجليزي)</label>
                            <input type="text" id="ctaButton1TextEn" name="ctaButton1TextEn" value="@Model.CtaButton1TextEn" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton1Link">رابط الزر</label>
                            <input type="text" id="ctaButton1Link" name="ctaButton1Link" value="@Model.CtaButton1Link" class="admin-form-input" placeholder="/investors">
                        </div>
                    </div>
                    
                    <!-- Button 2 -->
                    <div class="space-y-4">
                        <h5 class="font-medium text-gray-700 dark:text-gray-300">الزر الثاني</h5>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton2TextAr">نص الزر (عربي)</label>
                            <input type="text" id="ctaButton2TextAr" name="ctaButton2TextAr" value="@Model.CtaButton2TextAr" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton2TextEn">نص الزر (إنجليزي)</label>
                            <input type="text" id="ctaButton2TextEn" name="ctaButton2TextEn" value="@Model.CtaButton2TextEn" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="ctaButton2Link">رابط الزر</label>
                            <input type="text" id="ctaButton2Link" name="ctaButton2Link" value="@Model.CtaButton2Link" class="admin-form-input" placeholder="/about">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Section -->
    <div class="admin-card">
        <div class="admin-card-header">
            <div>
                <h3 class="admin-card-title">الإحصائيات</h3>
                <p class="admin-card-subtitle">الأرقام والإحصائيات المعروضة في الصفحة</p>
            </div>
            <button type="button" id="add-statistic" class="btn-primary">
                ➕ إضافة إحصائية
            </button>
        </div>
        
        <div id="statistics-container" class="space-y-4">
            @for (int i = 0; i < Model.Statistics.Count; i++)
            {
                <div class="statistic-item border border-gray-200 dark:border-gray-700 rounded-lg p-4" data-index="@i">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900 dark:text-white">إحصائية @(i + 1)</h4>
                        <div class="flex space-x-2 space-x-reverse">
                            <button type="button" class="btn-icon-secondary move-up" title="تحريك لأعلى">↑</button>
                            <button type="button" class="btn-icon-secondary move-down" title="تحريك لأسفل">↓</button>
                            <button type="button" class="btn-icon-danger remove-statistic" title="حذف">🗑️</button>
                        </div>
                    </div>
                    
                    <div class="admin-grid-3 gap-4">
                        <div class="admin-form-group">
                            <label class="admin-form-label">الرقم *</label>
                            <input type="text" name="statistics[@i].number" value="@Model.Statistics[i].Number" class="admin-form-input" required>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الوحدة</label>
                            <input type="text" name="statistics[@i].unit" value="@Model.Statistics[i].Unit" class="admin-form-input" placeholder="%" dir="ltr">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الأيقونة</label>
                            <input type="text" name="statistics[@i].icon" value="@Model.Statistics[i].Icon" class="admin-form-input" placeholder="💰">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">التسمية (عربي) *</label>
                            <input type="text" name="statistics[@i].labelAr" value="@Model.Statistics[i].LabelAr" class="admin-form-input" required>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">التسمية (إنجليزي) *</label>
                            <input type="text" name="statistics[@i].labelEn" value="@Model.Statistics[i].LabelEn" class="admin-form-input" required>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">اللون</label>
                            <select name="statistics[@i].color" class="admin-form-select">
                                <option value="blue" @(Model.Statistics[i].Color == "blue" ? "selected" : "")>أزرق</option>
                                <option value="green" @(Model.Statistics[i].Color == "green" ? "selected" : "")>أخضر</option>
                                <option value="purple" @(Model.Statistics[i].Color == "purple" ? "selected" : "")>بنفسجي</option>
                                <option value="orange" @(Model.Statistics[i].Color == "orange" ? "selected" : "")>برتقالي</option>
                                <option value="red" @(Model.Statistics[i].Color == "red" ? "selected" : "")>أحمر</option>
                                <option value="indigo" @(Model.Statistics[i].Color == "indigo" ? "selected" : "")>نيلي</option>
                            </select>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الوصف (عربي)</label>
                            <input type="text" name="statistics[@i].descriptionAr" value="@Model.Statistics[i].DescriptionAr" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الوصف (إنجليزي)</label>
                            <input type="text" name="statistics[@i].descriptionEn" value="@Model.Statistics[i].DescriptionEn" class="admin-form-input">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">نسبة التقدم (0-100)</label>
                            <input type="number" name="statistics[@i].progressPercentage" value="@Model.Statistics[i].ProgressPercentage" class="admin-form-input" min="0" max="100">
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="admin-card">
        <div class="admin-card-header">
            <div>
                <h3 class="admin-card-title">المميزات</h3>
                <p class="admin-card-subtitle">مميزات وخصائص المنصة</p>
            </div>
            <button type="button" id="add-feature" class="btn-primary">
                ➕ إضافة ميزة
            </button>
        </div>
        
        <div id="features-container" class="space-y-4">
            @for (int i = 0; i < Model.Features.Count; i++)
            {
                <div class="feature-item border border-gray-200 dark:border-gray-700 rounded-lg p-4" data-index="@i">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900 dark:text-white">ميزة @(i + 1)</h4>
                        <div class="flex space-x-2 space-x-reverse">
                            <label class="flex items-center">
                                <input type="checkbox" name="features[@i].isHighlighted" @(Model.Features[i].IsHighlighted ? "checked" : "") class="admin-form-checkbox ml-2">
                                <span class="text-sm">مميزة</span>
                            </label>
                            <button type="button" class="btn-icon-secondary move-up" title="تحريك لأعلى">↑</button>
                            <button type="button" class="btn-icon-secondary move-down" title="تحريك لأسفل">↓</button>
                            <button type="button" class="btn-icon-danger remove-feature" title="حذف">🗑️</button>
                        </div>
                    </div>
                    
                    <div class="admin-grid-2 gap-4">
                        <div class="admin-form-group">
                            <label class="admin-form-label">العنوان (عربي) *</label>
                            <input type="text" name="features[@i].titleAr" value="@Model.Features[i].TitleAr" class="admin-form-input" required>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">العنوان (إنجليزي) *</label>
                            <input type="text" name="features[@i].titleEn" value="@Model.Features[i].TitleEn" class="admin-form-input" required>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الوصف (عربي) *</label>
                            <textarea name="features[@i].descriptionAr" rows="3" class="admin-form-textarea" required>@Model.Features[i].DescriptionAr</textarea>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الوصف (إنجليزي) *</label>
                            <textarea name="features[@i].descriptionEn" rows="3" class="admin-form-textarea" required>@Model.Features[i].DescriptionEn</textarea>
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">الأيقونة</label>
                            <input type="text" name="features[@i].icon" value="@Model.Features[i].Icon" class="admin-form-input" placeholder="🛡️">
                        </div>
                        <div class="admin-form-group">
                            <label class="admin-form-label">اللون</label>
                            <select name="features[@i].color" class="admin-form-select">
                                <option value="blue" @(Model.Features[i].Color == "blue" ? "selected" : "")>أزرق</option>
                                <option value="green" @(Model.Features[i].Color == "green" ? "selected" : "")>أخضر</option>
                                <option value="purple" @(Model.Features[i].Color == "purple" ? "selected" : "")>بنفسجي</option>
                                <option value="orange" @(Model.Features[i].Color == "orange" ? "selected" : "")>برتقالي</option>
                                <option value="red" @(Model.Features[i].Color == "red" ? "selected" : "")>أحمر</option>
                                <option value="indigo" @(Model.Features[i].Color == "indigo" ? "selected" : "")>نيلي</option>
                                <option value="yellow" @(Model.Features[i].Color == "yellow" ? "selected" : "")>أصفر</option>
                                <option value="pink" @(Model.Features[i].Color == "pink" ? "selected" : "")>وردي</option>
                            </select>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</form>

@section Scripts {
    <script src="/js/admin-home-editor.js"></script>
}
