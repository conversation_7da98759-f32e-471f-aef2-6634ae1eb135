# مدد بلس - منصة التمويل الجماعي المتوافقة مع الشريعة

موقع إلكتروني حديث ومتجاوب وثنائي اللغة لشركة مدد بلس، وهي شركة سعودية مرخصة متخصصة في التمويل الجماعي القائم على الدين المتوافق مع الشريعة الإسلامية.

# MDD Plus - Sharia-Compliant Debt Crowdfunding Platform

A modern, responsive, and bilingual corporate website for MDD Plus, a licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding.

## 🚀 Advanced Features

### 🌐 **Complete Bilingual Support**
- **Dynamic Language Switching**: Instant language toggle with URL routing
- **RTL/LTR Support**: Full right-to-left and left-to-right layout support
- **Smart Language Detection**: Automatic detection from browser, URL, and cookies
- **Persistent Preferences**: Language choice saved across sessions
- **Keyboard Shortcuts**: Alt+L to toggle language, Alt+M for language menu

### 📱 **Advanced Responsive Design**
- **Mobile-First Approach**: Optimized for all device sizes (320px to 4K+)
- **Touch-Friendly Interface**: 44px minimum touch targets for accessibility
- **Adaptive Grid System**: Smart grid that adapts from 1 to 4 columns
- **Progressive Enhancement**: Works on all browsers with enhanced features for modern ones
- **Viewport Optimization**: Dynamic viewport height fixes for mobile browsers

### 🎨 **Modern UI/UX Features**
- **Advanced Animations**: Scroll-triggered animations with Intersection Observer
- **Staggered Animations**: Sequential element animations for better visual flow
- **Hover Effects**: Lift, glow, scale, and rotate effects
- **Loading States**: Skeleton screens and shimmer effects
- **Progress Indicators**: Advanced progress bars with animations
- **Gradient Designs**: Modern gradient backgrounds and text effects

### 🔧 **Progressive Web App (PWA)**
- **Installable**: Can be installed as a native app on mobile and desktop
- **Offline Support**: Service worker for offline functionality
- **App Manifest**: Full PWA manifest with icons and shortcuts
- **Push Notifications**: Support for web push notifications
- **Background Sync**: Sync data when connection is restored

### ♿ **Accessibility Features**
- **WCAG 2.1 Compliant**: Meets accessibility guidelines
- **Keyboard Navigation**: Full keyboard support with focus management
- **Screen Reader Support**: ARIA labels and live regions
- **High Contrast Mode**: Alt+C to toggle high contrast
- **Font Size Adjustment**: Alt+Plus/Minus to adjust font size
- **Focus Management**: Proper focus trapping in modals

### ⚡ **Performance Optimizations**
- **Lazy Loading**: Images and content loaded on demand
- **Resource Preloading**: Critical resources preloaded for faster rendering
- **Optimized Scrolling**: Throttled scroll events for smooth performance
- **Code Splitting**: JavaScript modules loaded as needed
- **Image Optimization**: Responsive images with proper sizing

### 🔔 **Advanced Notifications**
- **Toast Notifications**: Success, error, warning, and info messages
- **Auto-dismiss**: Configurable auto-dismiss timers
- **Action Buttons**: Interactive notifications with action buttons
- **Position Control**: Customizable notification positioning
- **Queue Management**: Multiple notifications handled gracefully

### 🎯 **Enhanced Interactions**
- **Advanced Tooltips**: Rich tooltips with positioning
- **Form Enhancements**: Floating labels and validation states
- **Smooth Scrolling**: Smooth anchor link navigation
- **Parallax Effects**: Subtle parallax scrolling effects
- **Gesture Support**: Touch gestures for mobile interactions

### 📊 **Analytics & Monitoring**
- **Performance Metrics**: Real-time performance monitoring
- **User Behavior Tracking**: Interaction and engagement metrics
- **Error Reporting**: Automatic error detection and reporting
- **Network Information**: Connection speed and type detection
- **Device Detection**: Automatic device and browser detection
- **Responsive Design**: Optimized for all devices and screen sizes
- **Dark/Light Mode**: Complete theme switching functionality
- **Umbraco CMS**: Built on Umbraco 16 for easy content management
- **Saudi Identity**: Inspired by Vision 2030, Manafa, and Tadawul design
- **Accessibility**: WCAG 2.1 compliant with full keyboard navigation
- **SEO Optimized**: Meta tags, structured data, and performance optimized

## 🎨 Design System

### Color Palette
- **Primary**: #005B82 (Deep Blue)
- **Accent**: #BCBEC0 (Soft Gray)
- **Text Primary**: #3C3C3B (Charcoal Gray)
- **Background (Light)**: #FFFFFF (White)
- **Background (Dark)**: #0F172A (Navy Black)
- **Accent (Dark Mode)**: #33B5E5 (Light Cyan)

### Typography
- **Arabic**: Tajawal, Cairo
- **English**: Inter, Roboto

## 🛠️ Tech Stack

- **Framework**: Umbraco CMS 16 (.NET 9)
- **Styling**: Tailwind CSS
- **Languages**: C#, HTML, CSS, JavaScript
- **Database**: SQL Server (configurable)
- **Hosting**: Azure/IIS compatible

## 📁 Project Structure

```
MddPlus/
├── Controllers/          # Custom controllers
├── Models/              # Content models and view models
├── Views/               # Razor views and templates
│   ├── Partials/        # Reusable partial views
│   │   └── MddPlus/     # MDD Plus specific partials
│   └── _Layout.cshtml   # Master layout
├── wwwroot/             # Static assets
│   ├── css/             # Stylesheets
│   ├── js/              # JavaScript files
│   ├── images/          # Images and graphics
│   └── fonts/           # Custom fonts
├── Properties/          # Project properties
├── appsettings.json     # Configuration
└── Program.cs           # Application startup
```

## 🚀 Getting Started

### Prerequisites
- .NET 9 SDK
- SQL Server (LocalDB for development)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mdd-plus
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Update connection string** (if needed)
   Edit `appsettings.json` to configure your database connection.

4. **Run the application**
   ```bash
   dotnet run
   ```

5. **Access the site**
   - Website: `https://localhost:5001`
   - Umbraco Backoffice: `https://localhost:5001/umbraco`

### First Time Setup

1. Navigate to `/umbraco` to complete the Umbraco installation
2. Create an admin user account
3. Configure languages (Arabic and English)
4. Import document types and content

## 🌐 Multilingual Configuration

The site supports Arabic (RTL) and English (LTR) with the following URL structure:
- English: `/en/` or default `/`
- Arabic: `/ar/`

### Language Switching
- Automatic detection based on browser preferences
- Manual switching via language toggle
- Persistent language preference via cookies

## 🎯 Key Pages

- **Home** - Hero section, features, statistics, CTA
- **About Us** - Company story, vision, mission, team
- **Services** - Investment and funding services
- **Investors** - Investment opportunities and guides
- **Borrowers** - Funding application process
- **FAQ** - Frequently asked questions
- **Contact** - Contact form and information
- **Legal** - Terms, privacy policy, compliance

## 🔧 Development

### Adding New Content Types

1. Create model in `Models/` directory
2. Add corresponding view in `Views/`
3. Configure in Umbraco backoffice

### Styling Guidelines

- Use Tailwind CSS utility classes
- Follow RTL/LTR responsive patterns
- Maintain dark/light mode compatibility
- Use custom CSS variables for brand colors

### JavaScript Development

- Main functionality in `wwwroot/js/main.js`
- Theme switching in `wwwroot/js/theme-toggle.js`
- Language switching in `wwwroot/js/language-switcher.js`

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px
- **Large Desktop**: > 1280px

## 🔒 Security & Compliance

- CMA licensing information displayed
- GDPR/Privacy policy compliance
- Secure form handling
- XSS protection
- CSRF protection via Umbraco

## 🚀 Deployment

### Azure Deployment
1. Configure Azure App Service
2. Set up SQL Azure database
3. Configure environment variables
4. Deploy via GitHub Actions or Azure DevOps

### IIS Deployment
1. Publish the application
2. Configure IIS site
3. Set up SQL Server database
4. Configure SSL certificate

## 📊 Performance

- Lazy loading for images
- Minified CSS/JS
- Optimized fonts loading
- Efficient caching strategies
- CDN ready

## 🧪 Testing

Run tests using:
```bash
dotnet test
```

## 📝 License

This project is proprietary software for MDD Plus.

## 🤝 Contributing

1. Follow coding standards
2. Maintain bilingual support
3. Test on multiple devices
4. Ensure accessibility compliance

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: Internal wiki
- Issues: Project management system

## 📱 **Device & Browser Support**

### **Mobile Devices**
- **iPhone**: iOS 12+ (Safari, Chrome)
- **Android**: Android 8+ (Chrome, Samsung Browser, Firefox)
- **Touch Optimization**: 44px minimum touch targets
- **Gesture Support**: Swipe, pinch, tap, long press
- **PWA Support**: Installable as native app

### **Tablets**
- **iPad**: iPadOS 13+ (Safari, Chrome)
- **Android Tablets**: Android 8+ (Chrome, Firefox)
- **Hybrid Interaction**: Touch and mouse support
- **Adaptive Layout**: 2-3 column responsive design

### **Desktop Browsers**
- **Chrome**: 90+ (Full support)
- **Firefox**: 88+ (Full support)
- **Safari**: 14+ (Full support)
- **Edge**: 90+ (Full support)
- **Internet Explorer**: 11 (Basic support)

## ⌨️ **Keyboard Shortcuts**

### **Language & Navigation**
- `Alt + L`: Toggle language (Arabic ↔️ English)
- `Alt + M`: Open language menu
- `Alt + H`: Go to homepage
- `Alt + S`: Go to services page

### **Accessibility**
- `Alt + C`: Toggle high contrast mode
- `Alt + +`: Increase font size
- `Alt + -`: Decrease font size
- `Tab`: Navigate through interactive elements
- `Esc`: Close modals and menus

## 📊 **Performance Metrics**

### **Core Web Vitals**
- **LCP**: < 2.5s (Largest Contentful Paint)
- **FID**: < 100ms (First Input Delay)
- **CLS**: < 0.1 (Cumulative Layout Shift)
- **FCP**: < 1.8s (First Contentful Paint)

### **Optimization Features**
- **Image Optimization**: WebP format with fallbacks
- **Code Splitting**: JavaScript modules loaded on demand
- **Resource Preloading**: Critical resources preloaded
- **Service Worker**: Offline support and caching

---

**MDD Plus** - Connecting investors and borrowers through trusted, Sharia-compliant financial solutions.

## 🏠 **Dynamic Content Management**

### **📊 Umbraco CMS Integration**
- **Document Types**: Fully structured content types for all pages
- **Multilingual Content**: Arabic/English content management
- **Nested Content**: Dynamic statistics, features, and service categories
- **Media Management**: Integrated image and file management
- **SEO Optimization**: Built-in meta tags and SEO fields

### **🔄 Content Models**
- **BasePageModel**: Common properties for all pages
- **HomePageModel**: Hero section, statistics, features, CTA
- **ServicesPageModel**: Service categories, process steps, features
- **StatisticItemModel**: Dynamic statistics with animations
- **FeatureItemModel**: Feature cards with icons and colors
- **ServiceCategoryModel**: Service types with pricing and features

### **📝 Content Management Features**
- **Visual Editor**: Rich text editing with media embedding
- **Drag & Drop**: Reorder content items easily
- **Preview Mode**: Preview changes before publishing
- **Version Control**: Content versioning and rollback
- **Workflow**: Content approval and publishing workflow
- **Multi-site**: Support for multiple sites and languages

### **🔗 Quick Access Links**
- 🏠 [Homepage (Arabic)](http://localhost:16385/)
- 🌍 [Homepage (English)](http://localhost:16385/en)
- 💼 [Services](http://localhost:16385/services)
- 🧪 [Test Page](http://localhost:16385/test)
- 🛠️ [Umbraco Admin](http://localhost:16385/umbraco)
- 🎛️ [Admin Panel](http://localhost:16385/admin)
- 📚 [Setup Guide](./UMBRACO_SETUP.md)
- 🎨 [Templates Guide](./UMBRACO_TEMPLATES_GUIDE.md)
- 📱 [Admin Panel Guide](./ADMIN_PANEL_GUIDE.md)

### **🔑 Admin Credentials**
- **Username**: <EMAIL>
- **Password**: MddPlus2024!
- **URL**: http://localhost:16385/umbraco

### **📊 Content Structure**
```
🏠 Home Page (homePageModel)
├── 🎆 Hero Section
│   ├── Title (AR/EN)
│   └── Subtitle (AR/EN)
├── 📊 Statistics (Nested Content)
│   ├── Number, Unit, Label, Description
│   ├── Color, Icon, Animation
│   └── Progress Percentage
├── ✨ Features (Nested Content)
│   ├── Title, Description (AR/EN)
│   ├── Icon, Color, Highlight
│   └── Link URL and Text
└── 📢 Call to Action
    ├── Title, Subtitle (AR/EN)
    └── Button Text and Links

💼 Services Page (servicesPageModel)
├── 🎆 Hero Section
├── 💰 Investors Section
├── 🏦 Borrowers Section
├── 🏢 Service Categories (Nested Content)
│   ├── Title, Description (AR/EN)
│   ├── Icon, Color, Popular Flag
│   ├── Max Amount, Payment Period
│   └── Features (Nested Content)
└── 🔄 Process Steps (Nested Content)
    ├── Step Number, Title (AR/EN)
    └── Description, Color
```
