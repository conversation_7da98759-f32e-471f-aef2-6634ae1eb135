# مدد بلس - منصة التمويل الجماعي المتوافقة مع الشريعة

موقع إلكتروني حديث ومتجاوب وثنائي اللغة لشركة مدد بلس، وهي شركة سعودية مرخصة متخصصة في التمويل الجماعي القائم على الدين المتوافق مع الشريعة الإسلامية.

# MDD Plus - Sharia-Compliant Debt Crowdfunding Platform

A modern, responsive, and bilingual corporate website for MDD Plus, a licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding.

## 🚀 Features

- **Bilingual Support**: Full Arabic (RTL) and English (LTR) support
- **Responsive Design**: Optimized for all devices and screen sizes
- **Dark/Light Mode**: Complete theme switching functionality
- **Umbraco CMS**: Built on Umbraco 16 for easy content management
- **Saudi Identity**: Inspired by Vision 2030, Manafa, and Tadawul design
- **Accessibility**: WCAG 2.1 compliant with full keyboard navigation
- **SEO Optimized**: Meta tags, structured data, and performance optimized

## 🎨 Design System

### Color Palette
- **Primary**: #005B82 (Deep Blue)
- **Accent**: #BCBEC0 (Soft Gray)
- **Text Primary**: #3C3C3B (Charcoal Gray)
- **Background (Light)**: #FFFFFF (White)
- **Background (Dark)**: #0F172A (Navy Black)
- **Accent (Dark Mode)**: #33B5E5 (Light Cyan)

### Typography
- **Arabic**: Tajawal, Cairo
- **English**: Inter, Roboto

## 🛠️ Tech Stack

- **Framework**: Umbraco CMS 16 (.NET 9)
- **Styling**: Tailwind CSS
- **Languages**: C#, HTML, CSS, JavaScript
- **Database**: SQL Server (configurable)
- **Hosting**: Azure/IIS compatible

## 📁 Project Structure

```
MddPlus/
├── Controllers/          # Custom controllers
├── Models/              # Content models and view models
├── Views/               # Razor views and templates
│   ├── Partials/        # Reusable partial views
│   │   └── MddPlus/     # MDD Plus specific partials
│   └── _Layout.cshtml   # Master layout
├── wwwroot/             # Static assets
│   ├── css/             # Stylesheets
│   ├── js/              # JavaScript files
│   ├── images/          # Images and graphics
│   └── fonts/           # Custom fonts
├── Properties/          # Project properties
├── appsettings.json     # Configuration
└── Program.cs           # Application startup
```

## 🚀 Getting Started

### Prerequisites
- .NET 9 SDK
- SQL Server (LocalDB for development)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mdd-plus
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Update connection string** (if needed)
   Edit `appsettings.json` to configure your database connection.

4. **Run the application**
   ```bash
   dotnet run
   ```

5. **Access the site**
   - Website: `https://localhost:5001`
   - Umbraco Backoffice: `https://localhost:5001/umbraco`

### First Time Setup

1. Navigate to `/umbraco` to complete the Umbraco installation
2. Create an admin user account
3. Configure languages (Arabic and English)
4. Import document types and content

## 🌐 Multilingual Configuration

The site supports Arabic (RTL) and English (LTR) with the following URL structure:
- English: `/en/` or default `/`
- Arabic: `/ar/`

### Language Switching
- Automatic detection based on browser preferences
- Manual switching via language toggle
- Persistent language preference via cookies

## 🎯 Key Pages

- **Home** - Hero section, features, statistics, CTA
- **About Us** - Company story, vision, mission, team
- **Services** - Investment and funding services
- **Investors** - Investment opportunities and guides
- **Borrowers** - Funding application process
- **FAQ** - Frequently asked questions
- **Contact** - Contact form and information
- **Legal** - Terms, privacy policy, compliance

## 🔧 Development

### Adding New Content Types

1. Create model in `Models/` directory
2. Add corresponding view in `Views/`
3. Configure in Umbraco backoffice

### Styling Guidelines

- Use Tailwind CSS utility classes
- Follow RTL/LTR responsive patterns
- Maintain dark/light mode compatibility
- Use custom CSS variables for brand colors

### JavaScript Development

- Main functionality in `wwwroot/js/main.js`
- Theme switching in `wwwroot/js/theme-toggle.js`
- Language switching in `wwwroot/js/language-switcher.js`

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px
- **Large Desktop**: > 1280px

## 🔒 Security & Compliance

- CMA licensing information displayed
- GDPR/Privacy policy compliance
- Secure form handling
- XSS protection
- CSRF protection via Umbraco

## 🚀 Deployment

### Azure Deployment
1. Configure Azure App Service
2. Set up SQL Azure database
3. Configure environment variables
4. Deploy via GitHub Actions or Azure DevOps

### IIS Deployment
1. Publish the application
2. Configure IIS site
3. Set up SQL Server database
4. Configure SSL certificate

## 📊 Performance

- Lazy loading for images
- Minified CSS/JS
- Optimized fonts loading
- Efficient caching strategies
- CDN ready

## 🧪 Testing

Run tests using:
```bash
dotnet test
```

## 📝 License

This project is proprietary software for MDD Plus.

## 🤝 Contributing

1. Follow coding standards
2. Maintain bilingual support
3. Test on multiple devices
4. Ensure accessibility compliance

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: Internal wiki
- Issues: Project management system

---

**MDD Plus** - Connecting investors and borrowers through trusted, Sharia-compliant financial solutions.
