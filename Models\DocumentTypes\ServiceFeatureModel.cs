using Umbraco.Cms.Core.Models.PublishedContent;

namespace MddPlus.Models.DocumentTypes
{
    public class ServiceFeatureModel : PublishedContentModel
    {
        public ServiceFeatureModel(IPublishedContent content) : base(content) { }

        public string TextAr => this.Value<string>("textAr") ?? "";
        public string TextEn => this.Value<string>("textEn") ?? "";
        public string Icon => this.Value<string>("icon") ?? "✓";
        public string Color => this.Value<string>("color") ?? "green";
        public bool IsHighlighted => this.Value<bool>("isHighlighted");
        public int SortOrder => this.Value<int>("sortOrder");
        
        public string GetText(string language = "ar")
        {
            return language == "ar" ? TextAr : TextEn;
        }
        
        public string GetIconColorClass()
        {
            return Color switch
            {
                "green" => "bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400",
                "blue" => "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400",
                "purple" => "bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400",
                "orange" => "bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400",
                "red" => "bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400",
                _ => "bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400"
            };
        }
        
        public string GetDotColorClass()
        {
            return Color switch
            {
                "green" => "bg-green-500",
                "blue" => "bg-blue-500",
                "purple" => "bg-purple-500",
                "orange" => "bg-orange-500",
                "red" => "bg-red-500",
                _ => "bg-green-500"
            };
        }
    }
}
