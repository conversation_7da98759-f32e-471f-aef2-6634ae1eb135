using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MddPlus.Models
{
    public partial class HomePage : PublishedContentModel
    {
        public HomePage(IPublishedContent content, IPublishedValueFallback publishedValueFallback)
            : base(content, publishedValueFallback)
        {
        }

        // Hero Section Properties
        public string? HeroTitle => this.Value<string>("heroTitle");
        public string? HeroSubtitle => this.Value<string>("heroSubtitle");
        public string? HeroDescription => this.Value<string>("heroDescription");
        public string? HeroImage => this.Value<string>("heroImage");
        public string? HeroPrimaryButtonText => this.Value<string>("heroPrimaryButtonText");
        public string? HeroPrimaryButtonUrl => this.Value<string>("heroPrimaryButtonUrl");
        public string? HeroSecondaryButtonText => this.Value<string>("heroSecondaryButtonText");
        public string? HeroSecondaryButtonUrl => this.Value<string>("heroSecondaryButtonUrl");

        // Statistics Properties
        public int TotalFunded => this.Value<int>("totalFunded");
        public int ActiveInvestors => this.Value<int>("activeInvestors");
        public int SuccessRate => this.Value<int>("successRate");
        public int AverageReturn => this.Value<int>("averageReturn");

        // Features Section
        public string? FeaturesTitle => this.Value<string>("featuresTitle");
        public string? FeaturesDescription => this.Value<string>("featuresDescription");
        public IEnumerable<FeatureItem>? Features => this.Value<IEnumerable<FeatureItem>>("features");

        // How It Works Section
        public string? HowItWorksTitle => this.Value<string>("howItWorksTitle");
        public string? HowItWorksDescription => this.Value<string>("howItWorksDescription");
        public IEnumerable<StepItem>? Steps => this.Value<IEnumerable<StepItem>>("steps");

        // CTA Section
        public string? CtaTitle => this.Value<string>("ctaTitle");
        public string? CtaDescription => this.Value<string>("ctaDescription");
        public string? CtaPrimaryButtonText => this.Value<string>("ctaPrimaryButtonText");
        public string? CtaPrimaryButtonUrl => this.Value<string>("ctaPrimaryButtonUrl");
        public string? CtaSecondaryButtonText => this.Value<string>("ctaSecondaryButtonText");
        public string? CtaSecondaryButtonUrl => this.Value<string>("ctaSecondaryButtonUrl");

        // SEO Properties
        public string? MetaTitle => this.Value<string>("metaTitle");
        public string? MetaDescription => this.Value<string>("metaDescription");
        public string? MetaKeywords => this.Value<string>("metaKeywords");
        public string? OgImage => this.Value<string>("ogImage");
    }

    public class FeatureItem
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Icon { get; set; }
        public string? IconType { get; set; } // svg, image, or icon-class
    }

    public class StepItem
    {
        public int StepNumber { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Icon { get; set; }
    }
}
