@using MddPlus.Models.DocumentTypes;
@using MddPlus.Helpers;
@model HomePageModel
@{
    Layout = "_Layout.cshtml";
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
    var isRtl = TranslationHelper.IsRTL(Context);
    var isArabic = currentLanguage == "ar";

    ViewBag.Title = Model?.GetTitle(currentLanguage) ?? "مدد بلس - منصة التمويل الجماعي";
    ViewBag.Description = Model?.GetDescription(currentLanguage) ?? "منصة التمويل الجماعي المتوافقة مع الشريعة الإسلامية";
}

@section Head {
    <meta property="og:title" content="@ViewBag.Title">
    <meta property="og:description" content="@ViewBag.Description">
    <meta property="og:type" content="website">
}

<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden" dir="rtl">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative container mx-auto px-4 py-20 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="text-right lg:text-right text-white animate-on-scroll">
                <!-- Saudi Vision 2030 Badge -->
                <div class="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                    <span class="text-sm font-medium">🇸🇦 رؤية المملكة 2030</span>
                </div>

                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight font-arabic">
                    @(Model?.GetHeroTitle(currentLanguage) ?? (isArabic ? "منصة التمويل الجماعي المتوافقة مع الشريعة" : "Sharia-Compliant Crowdfunding Platform"))
                </h1>

                <p class="text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                    @(Model?.GetHeroSubtitle(currentLanguage) ?? (isArabic ? "نربط المستثمرين والمقترضين من خلال حلول مالية مبتكرة" : "Connecting investors and borrowers through innovative solutions"))
                </p>

                <div class="flex flex-col sm:flex-row gap-4 mb-8">
                    <a href="@(Model?.CtaButton1Link ?? "/investors")" class="btn-primary text-lg px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-xl">
                        💰 @(Model?.GetCtaButton1Text(currentLanguage) ?? (isArabic ? "ابدأ الاستثمار الآن" : "Start Investing Now"))
                    </a>
                    <a href="@(Model?.CtaButton2Link ?? "/about")" class="btn-secondary text-lg px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-mdd-primary">
                        🏦 @(Model?.GetCtaButton2Text(currentLanguage) ?? (isArabic ? "تعرف على المنصة" : "Learn About Platform"))
                    </a>
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap items-center gap-6 text-sm">
                    <div class="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>مرخصة من هيئة السوق المالية</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>متوافقة مع الشريعة الإسلامية</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>آمنة ومضمونة 100%</span>
                    </div>
                </div>
            </div>

            <!-- Hero Image/Animation -->
            <div class="lg:order-first animate-on-scroll">
                <div class="relative">
                    <!-- Main Dashboard Mockup -->
                    <div class="w-full h-96 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-2xl shadow-2xl p-6 relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]">
                        </div>

                        <!-- Dashboard Content -->
                        <div class="relative z-10 h-full flex flex-col justify-between">
                            <!-- Header -->
                            <div class="flex items-center justify-between text-white">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                                        <span class="text-xl font-bold">م</span>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-lg">مدد بلس</h3>
                                        <p class="text-xs opacity-80">منصة التمويل الذكي</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs opacity-80">إجمالي الاستثمارات</p>
                                    <p class="text-xl font-bold">2.5 مليون ريال</p>
                                </div>
                            </div>

                            <!-- Stats Cards -->
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                                    <p class="text-white/80 text-xs">عائد شهري</p>
                                    <p class="text-white text-lg font-bold">+15.2%</p>
                                </div>
                                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                                    <p class="text-white/80 text-xs">مشاريع نشطة</p>
                                    <p class="text-white text-lg font-bold">12</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Cards Animation -->
                    <div class="absolute top-10 left-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 animate-bounce shadow-lg">
                        <div class="text-white text-center">
                            <div class="text-2xl font-bold">15.8%</div>
                            <div class="text-sm">عائد سنوي</div>
                        </div>
                    </div>

                    <div class="absolute bottom-10 right-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 animate-pulse shadow-lg">
                        <div class="text-white text-center">
                            <div class="text-2xl font-bold">500+</div>
                            <div class="text-sm">مليون ريال</div>
                        </div>
                    </div>

                    <!-- Additional floating element -->
                    <div class="absolute top-1/2 -left-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full p-3 animate-ping">
                        <div class="text-white text-center">
                            <div class="text-lg font-bold">📈</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-16 bg-white dark:bg-gray-900" dir="rtl">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">أرقام تتحدث عن نفسها</h2>
            <p class="text-gray-600 dark:text-gray-300">نتائج متميزة وعوائد مجزية لمستثمرينا</p>
        </div>

        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center animate-on-scroll">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 mb-4 shadow-lg">
                    <div class="stat-number text-white mb-2" data-target="750">750</div>
                    <div class="text-blue-100">مليون ريال</div>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 font-medium">إجمالي التمويل المقدم</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-6 mb-4 shadow-lg">
                    <div class="stat-number text-white mb-2" data-target="2500">2,500</div>
                    <div class="text-green-100">مستثمر</div>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 font-medium">مستثمر نشط في المنصة</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 mb-4 shadow-lg">
                    <div class="stat-number text-white mb-2" data-target="98">98</div>
                    <div class="text-purple-100">%</div>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 font-medium">معدل نجاح المشاريع</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl p-6 mb-4 shadow-lg">
                    <div class="stat-number text-white mb-2" data-target="15">15.8</div>
                    <div class="text-orange-100">%</div>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 font-medium">متوسط العائد السنوي</div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50 dark:bg-gray-800" dir="rtl">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                لماذا تختار مدد بلس؟
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                نقدم منصة آمنة وموثوقة للتمويل الجماعي المتوافق مع الشريعة الإسلامية، مع ضمانات قوية وعوائد مجزية
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-blue-100 dark:bg-blue-900">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">🔒 آمنة ومرخصة</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    مرخصة من هيئة السوق المالية السعودية مع أعلى معايير الأمان والحماية والشفافية
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-green-100 dark:bg-green-900">
                    <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6-10V7a2 2 0 11-4 0V3a2 2 0 114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">☪️ متوافقة مع الشريعة</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    جميع المنتجات والخدمات متوافقة مع أحكام الشريعة الإسلامية ومعتمدة من الهيئة الشرعية
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-purple-100 dark:bg-purple-900">
                    <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">📈 عوائد مجزية</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    عوائد تنافسية تصل إلى 15.8% سنوياً مع ضمانات قوية ومخاطر محسوبة
                </p>
            </div>

            <!-- Feature 4 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-orange-100 dark:bg-orange-900">
                    <svg class="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">🔍 شفافية كاملة</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    شفافية كاملة في جميع العمليات والاستثمارات مع تقارير دورية ومتابعة مباشرة
                </p>
            </div>

            <!-- Feature 5 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-red-100 dark:bg-red-900">
                    <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">👥 دعم متخصص</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    فريق متخصص من الخبراء الماليين لدعمك في رحلة الاستثمار على مدار الساعة
                </p>
            </div>

            <!-- Feature 6 -->
            <div class="card card-hover animate-on-scroll text-right bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300">
                <div class="feature-icon bg-indigo-100 dark:bg-indigo-900">
                    <svg class="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">📱 منصة سهلة الاستخدام</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    منصة رقمية سهلة الاستخدام مع تطبيق جوال متطور وواجهة عربية بالكامل
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 hero-gradient relative overflow-hidden" dir="rtl">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative container mx-auto px-4 text-center">
        <div class="max-w-4xl mx-auto text-white animate-on-scroll">
            <!-- Vision 2030 Integration -->
            <div class="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
                <span class="text-sm font-medium">🇸🇦 نساهم في تحقيق رؤية المملكة 2030</span>
            </div>

            <h2 class="text-3xl lg:text-5xl font-bold mb-6 leading-tight">
                ابدأ رحلة الاستثمار الذكي اليوم
            </h2>
            <p class="text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                انضم إلى أكثر من 2500 مستثمر يحققون عوائد مجزية تصل إلى 15.8% سنوياً من خلال منصتنا المتوافقة مع الشريعة
            </p>

            <!-- Key Benefits -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div class="text-2xl mb-2">💰</div>
                    <div class="font-bold">استثمار آمن</div>
                    <div class="text-sm opacity-80">مرخص من هيئة السوق المالية</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div class="text-2xl mb-2">☪️</div>
                    <div class="font-bold">متوافق شرعياً</div>
                    <div class="text-sm opacity-80">معتمد من الهيئة الشرعية</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div class="text-2xl mb-2">📈</div>
                    <div class="font-bold">عوائد مجزية</div>
                    <div class="text-sm opacity-80">حتى 15.8% عائد سنوي</div>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/investors" class="btn-primary text-lg px-10 py-4 bg-white text-mdd-primary hover:bg-gray-100 shadow-2xl transform hover:scale-105 transition-all duration-300">
                    🚀 ابدأ الاستثمار الآن
                </a>
                <a href="/about" class="btn-secondary text-lg px-10 py-4 border-2 border-white text-white hover:bg-white hover:text-mdd-primary transform hover:scale-105 transition-all duration-300">
                    📚 تعرف على المنصة
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-8 text-sm opacity-80">
                <p>مرخصة من هيئة السوق المالية • متوافقة مع الشريعة • آمنة 100%</p>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Simple animation for elements
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.animate-on-scroll');
            animateElements.forEach(el => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
                el.style.transition = 'all 0.6s ease-out';
            });
        });
    </script>
}
