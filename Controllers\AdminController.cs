using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MddPlus.Models.DocumentTypes;
using MddPlus.Models.Admin;
using MddPlus.Services;
using System.Text.Json;

namespace MddPlus.Controllers
{
    [Route("admin")]
    public class AdminController : Controller
    {
        private readonly ContentManagementService _contentService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(ContentManagementService contentService, ILogger<AdminController> logger)
        {
            _contentService = contentService;
            _logger = logger;
        }

        [Route("")]
        [Route("dashboard")]
        public IActionResult Dashboard()
        {
            var model = new AdminDashboardModel
            {
                TotalPages = _contentService.GetTotalPagesCount(),
                TotalStatistics = _contentService.GetTotalStatisticsCount(),
                TotalFeatures = _contentService.GetTotalFeaturesCount(),
                TotalServiceCategories = _contentService.GetTotalServiceCategoriesCount(),
                RecentActivities = _contentService.GetRecentActivities(),
                SystemStatus = _contentService.GetSystemStatus()
            };

            return View("~/Views/Admin/Dashboard.cshtml", model);
        }

        [Route("content")]
        public IActionResult ContentManagement()
        {
            var model = new ContentManagementModel
            {
                Pages = _contentService.GetAllPages(),
                Languages = new List<string> { "ar", "en" }
            };

            return View("~/Views/Admin/ContentManagement.cshtml", model);
        }

        [Route("content/home")]
        public IActionResult EditHomePage()
        {
            var model = _contentService.GetHomePageContent();
            return View("~/Views/Admin/EditHomePage.cshtml", model);
        }

        [HttpPost]
        [Route("content/home")]
        public async Task<IActionResult> SaveHomePage([FromBody] HomePageContentModel model)
        {
            try
            {
                await _contentService.SaveHomePageContent(model);
                return Json(new { success = true, message = "تم حفظ المحتوى بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving home page content");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ المحتوى" });
            }
        }

        [Route("content/services")]
        public IActionResult EditServicesPage()
        {
            var model = _contentService.GetServicesPageContent();
            return View("~/Views/Admin/EditServicesPage.cshtml", model);
        }

        [HttpPost]
        [Route("content/services")]
        public async Task<IActionResult> SaveServicesPage([FromBody] ServicesPageContentModel model)
        {
            try
            {
                await _contentService.SaveServicesPageContent(model);
                return Json(new { success = true, message = "تم حفظ المحتوى بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving services page content");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ المحتوى" });
            }
        }

        [Route("statistics")]
        public IActionResult ManageStatistics()
        {
            var model = _contentService.GetAllStatistics();
            return View("~/Views/Admin/ManageStatistics.cshtml", model);
        }

        [HttpPost]
        [Route("statistics")]
        public async Task<IActionResult> SaveStatistic([FromBody] StatisticItemContentModel model)
        {
            try
            {
                await _contentService.SaveStatistic(model);
                return Json(new { success = true, message = "تم حفظ الإحصائية بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving statistic");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ الإحصائية" });
            }
        }

        [HttpDelete]
        [Route("statistics/{id}")]
        public async Task<IActionResult> DeleteStatistic(int id)
        {
            try
            {
                await _contentService.DeleteStatistic(id);
                return Json(new { success = true, message = "تم حذف الإحصائية بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting statistic");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الإحصائية" });
            }
        }

        [Route("features")]
        public IActionResult ManageFeatures()
        {
            var model = _contentService.GetAllFeatures();
            return View("~/Views/Admin/ManageFeatures.cshtml", model);
        }

        [HttpPost]
        [Route("features")]
        public async Task<IActionResult> SaveFeature([FromBody] FeatureItemContentModel model)
        {
            try
            {
                await _contentService.SaveFeature(model);
                return Json(new { success = true, message = "تم حفظ الميزة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving feature");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ الميزة" });
            }
        }

        [HttpDelete]
        [Route("features/{id}")]
        public async Task<IActionResult> DeleteFeature(int id)
        {
            try
            {
                await _contentService.DeleteFeature(id);
                return Json(new { success = true, message = "تم حذف الميزة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting feature");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الميزة" });
            }
        }

        [Route("services")]
        public IActionResult ManageServices()
        {
            var model = _contentService.GetAllServiceCategories();
            return View("~/Views/Admin/ManageServices.cshtml", model);
        }

        [Route("media")]
        public IActionResult MediaManagement()
        {
            var model = _contentService.GetMediaFiles();
            return View("~/Views/Admin/MediaManagement.cshtml", model);
        }

        [HttpPost]
        [Route("media/upload")]
        public async Task<IActionResult> UploadMedia(IFormFile file)
        {
            try
            {
                var result = await _contentService.UploadMedia(file);
                return Json(new { success = true, url = result.Url, message = "تم رفع الملف بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading media");
                return Json(new { success = false, message = "حدث خطأ أثناء رفع الملف" });
            }
        }

        [Route("settings")]
        public IActionResult Settings()
        {
            var model = _contentService.GetSiteSettings();
            return View("~/Views/Admin/Settings.cshtml", model);
        }

        [HttpPost]
        [Route("settings")]
        public async Task<IActionResult> SaveSettings([FromBody] SiteSettingsModel model)
        {
            try
            {
                await _contentService.SaveSiteSettings(model);
                return Json(new { success = true, message = "تم حفظ الإعدادات بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving settings");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ الإعدادات" });
            }
        }

        [Route("preview/{pageType}")]
        public IActionResult PreviewPage(string pageType)
        {
            switch (pageType.ToLower())
            {
                case "home":
                    return RedirectToAction("Index", "Default");
                case "services":
                    return RedirectToAction("Services", "Default");
                default:
                    return RedirectToAction("Index", "Default");
            }
        }
    }
}
