// Home Page Editor JavaScript for MDD Plus Admin

class HomePageEditor {
    constructor() {
        this.statisticsCount = 0;
        this.featuresCount = 0;
        this.init();
    }
    
    init() {
        this.setupFormSubmission();
        this.setupStatisticsManager();
        this.setupFeaturesManager();
        this.setupSortable();
        this.initializeCounters();
    }
    
    initializeCounters() {
        this.statisticsCount = document.querySelectorAll('.statistic-item').length;
        this.featuresCount = document.querySelectorAll('.feature-item').length;
    }
    
    // Form Submission
    setupFormSubmission() {
        const form = document.getElementById('home-page-form');
        
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveHomePage();
            });
        }
    }
    
    async saveHomePage() {
        const form = document.getElementById('home-page-form');
        const formData = new FormData(form);
        
        // Collect statistics data
        const statistics = this.collectStatisticsData();
        
        // Collect features data
        const features = this.collectFeaturesData();
        
        const data = {
            id: 1,
            titleAr: formData.get('titleAr'),
            titleEn: formData.get('titleEn'),
            descriptionAr: formData.get('descriptionAr'),
            descriptionEn: formData.get('descriptionEn'),
            heroTitleAr: formData.get('heroTitleAr'),
            heroTitleEn: formData.get('heroTitleEn'),
            heroSubtitleAr: formData.get('heroSubtitleAr'),
            heroSubtitleEn: formData.get('heroSubtitleEn'),
            ctaTitleAr: formData.get('ctaTitleAr'),
            ctaTitleEn: formData.get('ctaTitleEn'),
            ctaSubtitleAr: formData.get('ctaSubtitleAr'),
            ctaSubtitleEn: formData.get('ctaSubtitleEn'),
            ctaButton1TextAr: formData.get('ctaButton1TextAr'),
            ctaButton1TextEn: formData.get('ctaButton1TextEn'),
            ctaButton1Link: formData.get('ctaButton1Link'),
            ctaButton2TextAr: formData.get('ctaButton2TextAr'),
            ctaButton2TextEn: formData.get('ctaButton2TextEn'),
            ctaButton2Link: formData.get('ctaButton2Link'),
            statistics: statistics,
            features: features
        };
        
        try {
            await window.adminPanel.apiRequest('/admin/content/home', {
                method: 'POST',
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.error('Error saving home page:', error);
        }
    }
    
    // Statistics Management
    setupStatisticsManager() {
        const addButton = document.getElementById('add-statistic');
        
        if (addButton) {
            addButton.addEventListener('click', () => {
                this.addStatistic();
            });
        }
        
        // Setup existing statistics
        this.setupStatisticEvents();
    }
    
    setupStatisticEvents() {
        const container = document.getElementById('statistics-container');
        
        if (container) {
            container.addEventListener('click', (e) => {
                if (e.target.classList.contains('remove-statistic')) {
                    this.removeStatistic(e.target.closest('.statistic-item'));
                } else if (e.target.classList.contains('move-up')) {
                    this.moveStatisticUp(e.target.closest('.statistic-item'));
                } else if (e.target.classList.contains('move-down')) {
                    this.moveStatisticDown(e.target.closest('.statistic-item'));
                }
            });
        }
    }
    
    addStatistic() {
        const container = document.getElementById('statistics-container');
        const index = this.statisticsCount;
        
        const statisticHtml = `
            <div class="statistic-item border border-gray-200 dark:border-gray-700 rounded-lg p-4" data-index="${index}">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900 dark:text-white">إحصائية ${index + 1}</h4>
                    <div class="flex space-x-2 space-x-reverse">
                        <button type="button" class="btn-icon-secondary move-up" title="تحريك لأعلى">↑</button>
                        <button type="button" class="btn-icon-secondary move-down" title="تحريك لأسفل">↓</button>
                        <button type="button" class="btn-icon-danger remove-statistic" title="حذف">🗑️</button>
                    </div>
                </div>
                
                <div class="admin-grid-3 gap-4">
                    <div class="admin-form-group">
                        <label class="admin-form-label">الرقم *</label>
                        <input type="text" name="statistics[${index}].number" class="admin-form-input" required>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الوحدة</label>
                        <input type="text" name="statistics[${index}].unit" class="admin-form-input" placeholder="%" dir="ltr">
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الأيقونة</label>
                        <input type="text" name="statistics[${index}].icon" class="admin-form-input" placeholder="💰">
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">التسمية (عربي) *</label>
                        <input type="text" name="statistics[${index}].labelAr" class="admin-form-input" required>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">التسمية (إنجليزي) *</label>
                        <input type="text" name="statistics[${index}].labelEn" class="admin-form-input" required>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">اللون</label>
                        <select name="statistics[${index}].color" class="admin-form-select">
                            <option value="blue">أزرق</option>
                            <option value="green">أخضر</option>
                            <option value="purple">بنفسجي</option>
                            <option value="orange">برتقالي</option>
                            <option value="red">أحمر</option>
                            <option value="indigo">نيلي</option>
                        </select>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الوصف (عربي)</label>
                        <input type="text" name="statistics[${index}].descriptionAr" class="admin-form-input">
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الوصف (إنجليزي)</label>
                        <input type="text" name="statistics[${index}].descriptionEn" class="admin-form-input">
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">نسبة التقدم (0-100)</label>
                        <input type="number" name="statistics[${index}].progressPercentage" class="admin-form-input" min="0" max="100" value="0">
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', statisticHtml);
        this.statisticsCount++;
        
        // Animate the new item
        const newItem = container.lastElementChild;
        newItem.style.opacity = '0';
        newItem.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            newItem.style.transition = 'all 0.3s ease';
            newItem.style.opacity = '1';
            newItem.style.transform = 'translateY(0)';
        }, 100);
    }
    
    removeStatistic(item) {
        if (confirm('هل أنت متأكد من حذف هذه الإحصائية؟')) {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                item.remove();
                this.updateStatisticIndexes();
            }, 300);
        }
    }
    
    moveStatisticUp(item) {
        const prev = item.previousElementSibling;
        if (prev) {
            item.parentNode.insertBefore(item, prev);
            this.updateStatisticIndexes();
        }
    }
    
    moveStatisticDown(item) {
        const next = item.nextElementSibling;
        if (next) {
            item.parentNode.insertBefore(next, item);
            this.updateStatisticIndexes();
        }
    }
    
    updateStatisticIndexes() {
        const items = document.querySelectorAll('.statistic-item');
        items.forEach((item, index) => {
            item.dataset.index = index;
            const title = item.querySelector('h4');
            if (title) {
                title.textContent = `إحصائية ${index + 1}`;
            }
            
            // Update input names
            const inputs = item.querySelectorAll('input, select');
            inputs.forEach(input => {
                const name = input.name;
                if (name && name.includes('statistics[')) {
                    input.name = name.replace(/statistics\[\d+\]/, `statistics[${index}]`);
                }
            });
        });
    }
    
    collectStatisticsData() {
        const statistics = [];
        const items = document.querySelectorAll('.statistic-item');
        
        items.forEach((item, index) => {
            const data = {
                id: index + 1,
                number: item.querySelector(`input[name*="number"]`).value,
                unit: item.querySelector(`input[name*="unit"]`).value,
                labelAr: item.querySelector(`input[name*="labelAr"]`).value,
                labelEn: item.querySelector(`input[name*="labelEn"]`).value,
                descriptionAr: item.querySelector(`input[name*="descriptionAr"]`).value,
                descriptionEn: item.querySelector(`input[name*="descriptionEn"]`).value,
                color: item.querySelector(`select[name*="color"]`).value,
                icon: item.querySelector(`input[name*="icon"]`).value,
                animationDelay: (index + 1) * 100,
                progressPercentage: parseInt(item.querySelector(`input[name*="progressPercentage"]`).value) || 0,
                sortOrder: index + 1
            };
            
            statistics.push(data);
        });
        
        return statistics;
    }
    
    // Features Management
    setupFeaturesManager() {
        const addButton = document.getElementById('add-feature');
        
        if (addButton) {
            addButton.addEventListener('click', () => {
                this.addFeature();
            });
        }
        
        // Setup existing features
        this.setupFeatureEvents();
    }
    
    setupFeatureEvents() {
        const container = document.getElementById('features-container');
        
        if (container) {
            container.addEventListener('click', (e) => {
                if (e.target.classList.contains('remove-feature')) {
                    this.removeFeature(e.target.closest('.feature-item'));
                } else if (e.target.classList.contains('move-up')) {
                    this.moveFeatureUp(e.target.closest('.feature-item'));
                } else if (e.target.classList.contains('move-down')) {
                    this.moveFeatureDown(e.target.closest('.feature-item'));
                }
            });
        }
    }
    
    addFeature() {
        const container = document.getElementById('features-container');
        const index = this.featuresCount;
        
        const featureHtml = `
            <div class="feature-item border border-gray-200 dark:border-gray-700 rounded-lg p-4" data-index="${index}">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900 dark:text-white">ميزة ${index + 1}</h4>
                    <div class="flex space-x-2 space-x-reverse">
                        <label class="flex items-center">
                            <input type="checkbox" name="features[${index}].isHighlighted" class="admin-form-checkbox ml-2">
                            <span class="text-sm">مميزة</span>
                        </label>
                        <button type="button" class="btn-icon-secondary move-up" title="تحريك لأعلى">↑</button>
                        <button type="button" class="btn-icon-secondary move-down" title="تحريك لأسفل">↓</button>
                        <button type="button" class="btn-icon-danger remove-feature" title="حذف">🗑️</button>
                    </div>
                </div>
                
                <div class="admin-grid-2 gap-4">
                    <div class="admin-form-group">
                        <label class="admin-form-label">العنوان (عربي) *</label>
                        <input type="text" name="features[${index}].titleAr" class="admin-form-input" required>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">العنوان (إنجليزي) *</label>
                        <input type="text" name="features[${index}].titleEn" class="admin-form-input" required>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الوصف (عربي) *</label>
                        <textarea name="features[${index}].descriptionAr" rows="3" class="admin-form-textarea" required></textarea>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الوصف (إنجليزي) *</label>
                        <textarea name="features[${index}].descriptionEn" rows="3" class="admin-form-textarea" required></textarea>
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">الأيقونة</label>
                        <input type="text" name="features[${index}].icon" class="admin-form-input" placeholder="🛡️">
                    </div>
                    <div class="admin-form-group">
                        <label class="admin-form-label">اللون</label>
                        <select name="features[${index}].color" class="admin-form-select">
                            <option value="blue">أزرق</option>
                            <option value="green">أخضر</option>
                            <option value="purple">بنفسجي</option>
                            <option value="orange">برتقالي</option>
                            <option value="red">أحمر</option>
                            <option value="indigo">نيلي</option>
                            <option value="yellow">أصفر</option>
                            <option value="pink">وردي</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', featureHtml);
        this.featuresCount++;
        
        // Animate the new item
        const newItem = container.lastElementChild;
        newItem.style.opacity = '0';
        newItem.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            newItem.style.transition = 'all 0.3s ease';
            newItem.style.opacity = '1';
            newItem.style.transform = 'translateY(0)';
        }, 100);
    }
    
    removeFeature(item) {
        if (confirm('هل أنت متأكد من حذف هذه الميزة؟')) {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                item.remove();
                this.updateFeatureIndexes();
            }, 300);
        }
    }
    
    moveFeatureUp(item) {
        const prev = item.previousElementSibling;
        if (prev) {
            item.parentNode.insertBefore(item, prev);
            this.updateFeatureIndexes();
        }
    }
    
    moveFeatureDown(item) {
        const next = item.nextElementSibling;
        if (next) {
            item.parentNode.insertBefore(next, item);
            this.updateFeatureIndexes();
        }
    }
    
    updateFeatureIndexes() {
        const items = document.querySelectorAll('.feature-item');
        items.forEach((item, index) => {
            item.dataset.index = index;
            const title = item.querySelector('h4');
            if (title) {
                title.textContent = `ميزة ${index + 1}`;
            }
            
            // Update input names
            const inputs = item.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                const name = input.name;
                if (name && name.includes('features[')) {
                    input.name = name.replace(/features\[\d+\]/, `features[${index}]`);
                }
            });
        });
    }
    
    collectFeaturesData() {
        const features = [];
        const items = document.querySelectorAll('.feature-item');
        
        items.forEach((item, index) => {
            const data = {
                id: index + 1,
                titleAr: item.querySelector(`input[name*="titleAr"]`).value,
                titleEn: item.querySelector(`input[name*="titleEn"]`).value,
                descriptionAr: item.querySelector(`textarea[name*="descriptionAr"]`).value,
                descriptionEn: item.querySelector(`textarea[name*="descriptionEn"]`).value,
                icon: item.querySelector(`input[name*="icon"]`).value,
                color: item.querySelector(`select[name*="color"]`).value,
                linkUrl: '',
                linkTextAr: '',
                linkTextEn: '',
                isHighlighted: item.querySelector(`input[name*="isHighlighted"]`).checked,
                sortOrder: index + 1
            };
            
            features.push(data);
        });
        
        return features;
    }
    
    // Sortable Setup
    setupSortable() {
        const statisticsContainer = document.getElementById('statistics-container');
        const featuresContainer = document.getElementById('features-container');
        
        if (statisticsContainer) {
            window.adminPanel.setupSortable(statisticsContainer, {
                onSort: () => this.updateStatisticIndexes()
            });
        }
        
        if (featuresContainer) {
            window.adminPanel.setupSortable(featuresContainer, {
                onSort: () => this.updateFeatureIndexes()
            });
        }
    }
}

// Initialize Home Page Editor
document.addEventListener('DOMContentLoaded', () => {
    window.homePageEditor = new HomePageEditor();
});
