using Umbraco.Cms.Core.Models.PublishedContent;

namespace MddPlus.Models.DocumentTypes
{
    public class StatisticItemModel : PublishedContentModel
    {
        public StatisticItemModel(IPublishedContent content) : base(content) { }

        public string Number => this.Value<string>("number") ?? "0";
        public string Unit => this.Value<string>("unit") ?? "";
        public string LabelAr => this.Value<string>("labelAr") ?? "";
        public string LabelEn => this.Value<string>("labelEn") ?? "";
        public string DescriptionAr => this.Value<string>("descriptionAr") ?? "";
        public string DescriptionEn => this.Value<string>("descriptionEn") ?? "";
        public string Color => this.Value<string>("color") ?? "blue";
        public string Icon => this.Value<string>("icon") ?? "📊";
        public int AnimationDelay => this.Value<int>("animationDelay");
        public int ProgressPercentage => this.Value<int>("progressPercentage");
        
        public string GetLabel(string language = "ar")
        {
            return language == "ar" ? LabelAr : LabelEn;
        }
        
        public string GetDescription(string language = "ar")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }
        
        public string GetColorClass()
        {
            return Color switch
            {
                "blue" => "text-blue-600 dark:text-blue-400",
                "green" => "text-green-600 dark:text-green-400",
                "purple" => "text-purple-600 dark:text-purple-400",
                "orange" => "text-orange-600 dark:text-orange-400",
                "red" => "text-red-600 dark:text-red-400",
                "indigo" => "text-indigo-600 dark:text-indigo-400",
                _ => "text-blue-600 dark:text-blue-400"
            };
        }
    }
}
