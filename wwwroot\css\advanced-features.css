/* Advanced Features CSS for MDD Plus */

/* PWA Styles */
.pwa-install-prompt {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: linear-gradient(135deg, #005B82 0%, #007BA7 100%);
    color: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 91, 130, 0.3);
    z-index: 1000;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.pwa-install-prompt.show {
    transform: translateY(0);
    opacity: 1;
}

.pwa-install-prompt .close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Advanced Animations */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        transform: rotate(-180deg) scale(0.8);
        opacity: 0;
    }
    to {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
}

/* Animation Classes */
.animate-slide-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-top {
    animation: slideInFromTop 0.6s ease-out;
}

.animate-slide-bottom {
    animation: slideInFromBottom 0.6s ease-out;
}

.animate-zoom {
    animation: zoomIn 0.5s ease-out;
}

.animate-rotate {
    animation: rotateIn 0.8s ease-out;
}

/* Staggered Animations */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Advanced Loading States */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.dark .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Advanced Hover Effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 30px rgba(0, 91, 130, 0.3);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Advanced Gradients */
.gradient-primary {
    background: linear-gradient(135deg, #005B82 0%, #007BA7 50%, #33B5E5 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, #BCBEC0 0%, #E5E7EB 50%, #F3F4F6 100%);
}

.gradient-success {
    background: linear-gradient(135deg, #10B981 0%, #34D399 50%, #6EE7B7 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #FBBF24 50%, #FCD34D 100%);
}

.gradient-error {
    background: linear-gradient(135deg, #EF4444 0%, #F87171 50%, #FCA5A5 100%);
}

/* Advanced Borders */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #005B82, #33B5E5) border-box;
}

.dark .border-gradient {
    background: linear-gradient(#1F2937, #1F2937) padding-box,
                linear-gradient(135deg, #005B82, #33B5E5) border-box;
}

/* Advanced Shadows */
.shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
}

.shadow-hard {
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.16);
}

.shadow-colored {
    box-shadow: 0 8px 32px rgba(0, 91, 130, 0.2);
}

/* Advanced Typography */
.text-gradient {
    background: linear-gradient(135deg, #005B82 0%, #33B5E5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-strong {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Advanced Buttons */
.btn-advanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-advanced:hover::before {
    left: 100%;
}

.btn-floating {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #005B82 0%, #007BA7 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 91, 130, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.btn-floating:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 91, 130, 0.4);
}

/* Advanced Cards */
.card-advanced {
    position: relative;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.dark .card-advanced {
    background: #1F2937;
}

.card-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #005B82, #33B5E5);
}

.card-advanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Advanced Forms */
.form-advanced .form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-advanced .form-input {
    width: 100%;
    padding: 16px;
    border: 2px solid #E5E7EB;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
}

.dark .form-advanced .form-input {
    background: #374151;
    border-color: #4B5563;
    color: white;
}

.form-advanced .form-input:focus {
    outline: none;
    border-color: #005B82;
    box-shadow: 0 0 0 3px rgba(0, 91, 130, 0.1);
}

.form-advanced .form-label {
    position: absolute;
    top: 16px;
    left: 16px;
    color: #6B7280;
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-advanced .form-input:focus + .form-label,
.form-advanced .form-input:not(:placeholder-shown) + .form-label {
    top: -8px;
    left: 12px;
    font-size: 12px;
    color: #005B82;
    background: white;
    padding: 0 4px;
}

.dark .form-advanced .form-input:focus + .form-label,
.dark .form-advanced .form-input:not(:placeholder-shown) + .form-label {
    background: #1F2937;
}

/* Advanced Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 16px;
    transform: translateX(100%);
    transition: all 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #10B981;
}

.notification.error {
    border-left: 4px solid #EF4444;
}

.notification.warning {
    border-left: 4px solid #F59E0B;
}

.notification.info {
    border-left: 4px solid #3B82F6;
}

/* Advanced Progress Bars */
.progress-advanced {
    width: 100%;
    height: 8px;
    background: #E5E7EB;
    border-radius: 4px;
    overflow: hidden;
}

.progress-advanced .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #005B82, #33B5E5);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-advanced .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Advanced Tooltips */
.tooltip-advanced {
    position: relative;
    cursor: pointer;
}

.tooltip-advanced::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1F2937;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-advanced::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1F2937;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip-advanced:hover::before,
.tooltip-advanced:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Print Styles */
@media print {
    .no-print,
    .btn-floating,
    .pwa-install-prompt,
    .notification {
        display: none !important;
    }
    
    .card-advanced {
        box-shadow: none !important;
        border: 1px solid #E5E7EB !important;
    }
    
    .gradient-primary,
    .gradient-secondary,
    .gradient-success,
    .gradient-warning,
    .gradient-error {
        background: #F3F4F6 !important;
        color: #1F2937 !important;
    }
}
