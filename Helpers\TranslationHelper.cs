using MddPlus.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Globalization;

namespace MddPlus.Helpers
{
    public static class TranslationHelper
    {
        private static LanguageService _languageService = new LanguageService();

        public static string T(this IHtmlHelper htmlHelper, string key)
        {
            var httpContext = htmlHelper.ViewContext.HttpContext;
            var currentLanguage = GetCurrentLanguage(httpContext);
            return _languageService.GetTranslation(key, currentLanguage);
        }

        public static string GetCurrentLanguage(HttpContext httpContext)
        {
            // Check URL path for language
            var path = httpContext.Request.Path.Value;
            if (path.StartsWith("/en"))
                return "en";
            if (path.StartsWith("/ar"))
                return "ar";

            // Check cookie
            if (httpContext.Request.Cookies.TryGetValue("language", out var cookieLanguage))
            {
                return cookieLanguage;
            }

            // Check Accept-Language header
            var acceptLanguage = httpContext.Request.Headers["Accept-Language"].FirstOrDefault();
            if (!string.IsNullOrEmpty(acceptLanguage))
            {
                if (acceptLanguage.Contains("ar"))
                    return "ar";
                if (acceptLanguage.Contains("en"))
                    return "en";
            }

            // Default to Arabic
            return "ar";
        }

        public static bool IsRTL(HttpContext httpContext)
        {
            var currentLanguage = GetCurrentLanguage(httpContext);
            return _languageService.IsRTL(currentLanguage);
        }

        public static string GetDirection(HttpContext httpContext)
        {
            var currentLanguage = GetCurrentLanguage(httpContext);
            return _languageService.GetLanguageDirection(currentLanguage);
        }

        public static string GetFontFamily(HttpContext httpContext)
        {
            var currentLanguage = GetCurrentLanguage(httpContext);
            return _languageService.GetFontFamily(currentLanguage);
        }

        public static string GetOppositeLanguage(HttpContext httpContext)
        {
            var currentLanguage = GetCurrentLanguage(httpContext);
            return currentLanguage == "ar" ? "en" : "ar";
        }

        public static string GetOppositeLanguageName(HttpContext httpContext)
        {
            var oppositeLanguage = GetOppositeLanguage(httpContext);
            return oppositeLanguage == "ar" ? "العربية" : "English";
        }

        public static string GetLanguageUrl(HttpContext httpContext, string targetLanguage)
        {
            var currentPath = httpContext.Request.Path.Value;
            var currentLanguage = GetCurrentLanguage(httpContext);

            // Remove current language prefix if exists
            if (currentPath.StartsWith($"/{currentLanguage}"))
            {
                currentPath = currentPath.Substring(3);
            }

            // Add new language prefix
            if (targetLanguage != "ar") // Arabic is default, no prefix needed
            {
                return $"/{targetLanguage}{currentPath}";
            }

            return currentPath == "" ? "/" : currentPath;
        }
    }
}
