@using MddPlus.Models.DocumentTypes;
@using MddPlus.Helpers;
@model ServicesPageModel
@{
    Layout = "_Layout.cshtml";
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
    var isRtl = TranslationHelper.IsRTL(Context);
    var isArabic = currentLanguage == "ar";
    
    ViewBag.Title = Model.GetTitle(currentLanguage);
    ViewBag.Description = Model.GetDescription(currentLanguage);
}

@section Head {
    <meta property="og:title" content="@ViewBag.Title">
    <meta property="og:description" content="@ViewBag.Description">
    <meta property="og:type" content="website">
}

<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden section-padding" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative container mx-auto">
        <div class="text-center text-white max-w-4xl mx-auto">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
                <span class="text-sm font-medium">💼 @(isArabic ? "خدمات مالية متطورة" : "Advanced Financial Services")</span>
            </div>
            
            <h1 class="text-4xl lg:text-5xl font-bold mb-6 @(isArabic ? "font-arabic" : "font-english")">
                @Model.GetHeroTitle(currentLanguage)
            </h1>
            <p class="text-xl lg:text-2xl text-gray-200 leading-relaxed">
                @Model.GetHeroSubtitle(currentLanguage)
            </p>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white dark:bg-gray-900" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="container mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- For Investors -->
            <div class="text-@(isRtl ? "right" : "left")">
                <div class="inline-flex items-center bg-blue-100 dark:bg-blue-900 rounded-full px-4 py-2 mb-6">
                    <span class="text-blue-600 dark:text-blue-400 font-medium">💰 @(isArabic ? "للمستثمرين" : "For Investors")</span>
                </div>
                
                <h2 class="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
                    @Model.GetInvestorsTitle(currentLanguage)
                </h2>
                
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                    @Model.GetInvestorsDescription(currentLanguage)
                </p>
                
                <div class="space-y-4">
                    @foreach (var feature in Model.InvestorsFeatures.OrderBy(f => f.SortOrder))
                    {
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 @feature.GetIconColorClass() rounded-full flex items-center justify-center">
                                <span class="text-sm">@feature.Icon</span>
                            </div>
                            <span class="text-gray-700 dark:text-gray-300">@feature.GetText(currentLanguage)</span>
                        </div>
                    }
                </div>
                
                <div class="mt-8">
                    <a href="/investors" class="btn-primary bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                        🚀 @(isArabic ? "ابدأ الاستثمار الآن" : "Start Investing Now")
                    </a>
                </div>
            </div>
            
            <!-- For Borrowers -->
            <div class="text-@(isRtl ? "right" : "left")">
                <div class="inline-flex items-center bg-purple-100 dark:bg-purple-900 rounded-full px-4 py-2 mb-6">
                    <span class="text-purple-600 dark:text-purple-400 font-medium">🏦 @(isArabic ? "للمقترضين" : "For Borrowers")</span>
                </div>
                
                <h2 class="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
                    @Model.GetBorrowersTitle(currentLanguage)
                </h2>
                
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                    @Model.GetBorrowersDescription(currentLanguage)
                </p>
                
                <div class="space-y-4">
                    @foreach (var feature in Model.BorrowersFeatures.OrderBy(f => f.SortOrder))
                    {
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 @feature.GetIconColorClass() rounded-full flex items-center justify-center">
                                <span class="text-sm">@feature.Icon</span>
                            </div>
                            <span class="text-gray-700 dark:text-gray-300">@feature.GetText(currentLanguage)</span>
                        </div>
                    }
                </div>
                
                <div class="mt-8">
                    <a href="/borrowers" class="btn-primary bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800">
                        📋 @(isArabic ? "اطلب التمويل الآن" : "Request Funding Now")
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Categories -->
<section class="section-padding bg-gray-50 dark:bg-gray-800" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                @(isArabic ? "أنواع التمويل المتاحة" : "Available Funding Types")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? "نقدم أنواع متعددة من التمويل لتلبية احتياجات مختلف القطاعات والمشاريع" : "We offer multiple types of funding to meet the needs of different sectors and projects")
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach (var category in Model.ServiceCategories.OrderBy(c => c.SortOrder))
            {
                <div class="card-advanced text-@(isRtl ? "right" : "left") bg-white dark:bg-gray-800 hover:shadow-2xl transition-all duration-300 @(category.IsPopular ? "border-2 border-yellow-400" : "")">
                    @if (category.IsPopular)
                    {
                        <div class="absolute -top-3 @(isRtl ? "right-4" : "left-4") bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
                            @(isArabic ? "الأكثر طلباً" : "Most Popular")
                        </div>
                    }
                    
                    <div class="w-16 h-16 bg-gradient-to-br @category.GetGradientClass() rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-2xl">@category.Icon</span>
                    </div>
                    
                    <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                        @category.GetTitle(currentLanguage)
                    </h3>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                        @category.GetDescription(currentLanguage)
                    </p>
                    
                    <div class="space-y-2 text-sm mb-6">
                        @if (!string.IsNullOrEmpty(category.MaxAmount))
                        {
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 @category.GetDotColorClass() rounded-full"></span>
                                <span>@category.MaxAmount</span>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(category.PaymentPeriod))
                        {
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 @category.GetDotColorClass() rounded-full"></span>
                                <span>@category.PaymentPeriod</span>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(category.InterestRate))
                        {
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 @category.GetDotColorClass() rounded-full"></span>
                                <span>@category.InterestRate</span>
                            </div>
                        }
                    </div>
                    
                    @if (category.Features.Any())
                    {
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <h4 class="font-semibold mb-2 text-gray-900 dark:text-white">
                                @(isArabic ? "المميزات:" : "Features:")
                            </h4>
                            @foreach (var feature in category.Features.Take(3))
                            {
                                <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 mb-1">
                                    <span class="text-green-500">@feature.Icon</span>
                                    <span>@feature.GetText(currentLanguage)</span>
                                </div>
                            }
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(category.LinkUrl))
                    {
                        <div class="mt-6">
                            <a href="@category.LinkUrl" class="btn-primary w-full text-center bg-gradient-to-r @category.GetGradientClass()">
                                @(isArabic ? "اعرف المزيد" : "Learn More")
                            </a>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</section>

<!-- Process Steps -->
<section class="section-padding bg-white dark:bg-gray-900" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                @(isArabic ? "كيف تعمل العملية؟" : "How Does the Process Work?")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? "عملية بسيطة وسريعة للحصول على التمويل أو بدء الاستثمار" : "Simple and fast process to get funding or start investing")
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach (var step in Model.ProcessSteps.OrderBy(s => s.SortOrder))
            {
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br @step.GetGradientClass() text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg">
                        @step.StepNumber
                    </div>
                    <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                        @step.GetTitle(currentLanguage)
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300">
                        @step.GetDescription(currentLanguage)
                    </p>
                </div>
            }
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding hero-gradient relative overflow-hidden" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative container mx-auto text-center">
        <div class="max-w-4xl mx-auto text-white">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                @(isArabic ? "هل أنت مستعد لبدء رحلتك المالية؟" : "Are You Ready to Start Your Financial Journey?")
            </h2>
            <p class="text-xl mb-8 text-gray-200">
                @(isArabic ? "انضم إلى آلاف العملاء الذين يثقون في خدماتنا المالية المتوافقة مع الشريعة" : "Join thousands of clients who trust our Sharia-compliant financial services")
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/investors" class="btn-primary text-lg px-10 py-4 bg-white text-mdd-primary hover:bg-gray-100 shadow-2xl transform hover:scale-105 transition-all duration-300">
                    💰 @(isArabic ? "ابدأ الاستثمار" : "Start Investing")
                </a>
                <a href="/borrowers" class="btn-secondary text-lg px-10 py-4 border-2 border-white text-white hover:bg-white hover:text-mdd-primary transform hover:scale-105 transition-all duration-300">
                    🏦 @(isArabic ? "اطلب تمويل" : "Request Funding")
                </a>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Services page specific scripts
        document.addEventListener('DOMContentLoaded', function() {
            // Animate service cards on scroll
            const serviceCards = document.querySelectorAll('.card-advanced');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.classList.add('animate-slide-bottom');
                        }, index * 100);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            serviceCards.forEach(card => observer.observe(card));
            
            // Popular service highlight animation
            const popularCards = document.querySelectorAll('.border-yellow-400');
            popularCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('animate-pulse');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('animate-pulse');
                });
            });
        });
    </script>
}
