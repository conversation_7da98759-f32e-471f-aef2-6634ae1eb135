/* RTL (Right-to-Left) Specific Styles for Arabic */

/* Base RTL Adjustments */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  font-family: '<PERSON><PERSON>wal', 'Cairo', sans-serif;
}

/* Navigation RTL */
[dir="rtl"] .nav-link::after {
  right: 0;
  left: auto;
}

/* Button RTL Adjustments */
[dir="rtl"] .btn-primary,
[dir="rtl"] .btn-secondary,
[dir="rtl"] .btn-accent {
  direction: rtl;
}

/* Form RTL */
[dir="rtl"] .form-input {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .form-label {
  text-align: right;
}

/* Card RTL */
[dir="rtl"] .card {
  text-align: right;
}

/* Hero Section RTL */
[dir="rtl"] .hero-content {
  text-align: right;
}

/* Feature Cards RTL */
[dir="rtl"] .feature-card {
  text-align: right;
}

[dir="rtl"] .feature-icon {
  margin-left: 0;
  margin-right: auto;
}

/* Stats RTL */
[dir="rtl"] .stat-item {
  text-align: right;
}

/* Footer RTL */
[dir="rtl"] .footer-section {
  text-align: right;
}

/* Mobile Menu RTL */
[dir="rtl"] .mobile-nav-link {
  text-align: right;
}

/* Breadcrumb RTL */
[dir="rtl"] .breadcrumb {
  direction: rtl;
}

[dir="rtl"] .breadcrumb-separator::before {
  content: "\\";
  transform: scaleX(-1);
}

/* Table RTL */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

/* List RTL */
[dir="rtl"] ul,
[dir="rtl"] ol {
  padding-right: 1.5rem;
  padding-left: 0;
}

[dir="rtl"] li {
  text-align: right;
}

/* Tooltip RTL */
[dir="rtl"] .tooltip {
  direction: rtl;
  text-align: right;
}

/* Modal RTL */
[dir="rtl"] .modal-content {
  text-align: right;
}

[dir="rtl"] .modal-header {
  text-align: right;
}

[dir="rtl"] .modal-body {
  text-align: right;
}

[dir="rtl"] .modal-footer {
  text-align: right;
  justify-content: flex-start;
}

/* Dropdown RTL */
[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}

/* Pagination RTL */
[dir="rtl"] .pagination {
  direction: rtl;
}

/* Progress Bar RTL */
[dir="rtl"] .progress-bar {
  direction: rtl;
}

/* Tabs RTL */
[dir="rtl"] .tab-content {
  text-align: right;
}

[dir="rtl"] .tab-pane {
  text-align: right;
}

/* Alert RTL */
[dir="rtl"] .alert {
  text-align: right;
}

/* Badge RTL */
[dir="rtl"] .badge {
  direction: rtl;
}

/* Carousel RTL */
[dir="rtl"] .carousel-control-prev {
  right: 0;
  left: auto;
}

[dir="rtl"] .carousel-control-next {
  left: 0;
  right: auto;
}

[dir="rtl"] .carousel-indicators {
  direction: rtl;
}

/* Accordion RTL */
[dir="rtl"] .accordion-header {
  text-align: right;
}

[dir="rtl"] .accordion-body {
  text-align: right;
}

/* Timeline RTL */
[dir="rtl"] .timeline {
  direction: rtl;
}

[dir="rtl"] .timeline-item {
  text-align: right;
}

/* Search RTL */
[dir="rtl"] .search-input {
  text-align: right;
  direction: rtl;
}

/* Social Media RTL */
[dir="rtl"] .social-links {
  direction: rtl;
}

/* Contact Form RTL */
[dir="rtl"] .contact-form {
  text-align: right;
}

[dir="rtl"] .contact-form input,
[dir="rtl"] .contact-form textarea,
[dir="rtl"] .contact-form select {
  text-align: right;
  direction: rtl;
}

/* FAQ RTL */
[dir="rtl"] .faq-item {
  text-align: right;
}

[dir="rtl"] .faq-question {
  text-align: right;
}

[dir="rtl"] .faq-answer {
  text-align: right;
}

/* Investment Cards RTL */
[dir="rtl"] .investment-card {
  text-align: right;
}

[dir="rtl"] .investment-details {
  text-align: right;
}

/* Service Cards RTL */
[dir="rtl"] .service-card {
  text-align: right;
}

[dir="rtl"] .service-description {
  text-align: right;
}

/* Team Cards RTL */
[dir="rtl"] .team-card {
  text-align: right;
}

[dir="rtl"] .team-info {
  text-align: right;
}

/* Testimonial RTL */
[dir="rtl"] .testimonial {
  text-align: right;
}

[dir="rtl"] .testimonial-content {
  text-align: right;
}

[dir="rtl"] .testimonial-author {
  text-align: right;
}

/* Blog RTL */
[dir="rtl"] .blog-post {
  text-align: right;
}

[dir="rtl"] .blog-content {
  text-align: right;
}

[dir="rtl"] .blog-meta {
  text-align: right;
}

/* Sidebar RTL */
[dir="rtl"] .sidebar {
  text-align: right;
}

[dir="rtl"] .sidebar-widget {
  text-align: right;
}

/* Footer RTL Specific */
[dir="rtl"] .footer-links {
  text-align: right;
}

[dir="rtl"] .footer-social {
  direction: rtl;
}

[dir="rtl"] .footer-bottom {
  text-align: right;
}

/* Custom RTL Utilities */
.rtl-text-right {
  text-align: right !important;
}

.rtl-text-left {
  text-align: left !important;
}

.rtl-float-right {
  float: right !important;
}

.rtl-float-left {
  float: left !important;
}

.rtl-margin-right {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}

.rtl-margin-left {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}

.rtl-padding-right {
  padding-right: 1rem !important;
  padding-left: 0 !important;
}

.rtl-padding-left {
  padding-left: 1rem !important;
  padding-right: 0 !important;
}

/* Arabic Number Formatting */
[dir="rtl"] .arabic-numbers {
  font-feature-settings: "lnum" 1;
}

/* Arabic Text Improvements */
[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] div {
  line-height: 1.8;
  word-spacing: 0.1em;
}

[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  line-height: 1.4;
  font-weight: 700;
}

/* Responsive RTL Adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .mobile-nav {
    text-align: right;
  }
  
  [dir="rtl"] .mobile-menu-item {
    text-align: right;
  }
  
  [dir="rtl"] .mobile-cta {
    text-align: center;
  }
}

@media (max-width: 640px) {
  [dir="rtl"] .hero-title {
    text-align: center;
  }
  
  [dir="rtl"] .hero-description {
    text-align: center;
  }
  
  [dir="rtl"] .feature-grid {
    text-align: center;
  }
}
