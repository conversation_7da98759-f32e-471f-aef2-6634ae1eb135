using Umbraco.Cms.Core.Models.PublishedContent;

namespace MddPlus.Models.DocumentTypes
{
    public class FeatureItemModel : PublishedContentModel
    {
        public FeatureItemModel(IPublishedContent content) : base(content) { }

        public string TitleAr => this.Value<string>("titleAr") ?? "";
        public string TitleEn => this.Value<string>("titleEn") ?? "";
        public string DescriptionAr => this.Value<string>("descriptionAr") ?? "";
        public string DescriptionEn => this.Value<string>("descriptionEn") ?? "";
        public string Icon => this.Value<string>("icon") ?? "🔧";
        public string Color => this.Value<string>("color") ?? "blue";
        public IPublishedContent Image => this.Value<IPublishedContent>("image");
        public string LinkUrl => this.Value<string>("linkUrl") ?? "";
        public string LinkTextAr => this.Value<string>("linkTextAr") ?? "";
        public string LinkTextEn => this.Value<string>("linkTextEn") ?? "";
        public bool IsHighlighted => this.Value<bool>("isHighlighted");
        public int SortOrder => this.Value<int>("sortOrder");
        
        public string GetTitle(string language = "ar")
        {
            return language == "ar" ? TitleAr : TitleEn;
        }
        
        public string GetDescription(string language = "ar")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }
        
        public string GetLinkText(string language = "ar")
        {
            return language == "ar" ? LinkTextAr : LinkTextEn;
        }
        
        public string GetGradientClass()
        {
            return Color switch
            {
                "blue" => "from-blue-500 to-blue-600",
                "green" => "from-green-500 to-green-600",
                "purple" => "from-purple-500 to-purple-600",
                "orange" => "from-orange-500 to-orange-600",
                "red" => "from-red-500 to-red-600",
                "indigo" => "from-indigo-500 to-indigo-600",
                "yellow" => "from-yellow-500 to-yellow-600",
                "pink" => "from-pink-500 to-pink-600",
                _ => "from-blue-500 to-blue-600"
            };
        }
    }
}
