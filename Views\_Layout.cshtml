@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = null;
    var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
    var isRtl = currentCulture.TextInfo.IsRightToLeft;
    var langCode = currentCulture.TwoLetterISOLanguageName;
    var direction = isRtl ? "rtl" : "ltr";
}
<!DOCTYPE html>
<html lang="@langCode" dir="@direction">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@(ViewBag.Title ?? "MDD Plus - Sharia-Compliant Debt Crowdfunding")</title>
    <meta name="description" content="@(ViewBag.Description ?? "Licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding. Connecting investors and borrowers through trusted financial solutions.")">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    @if (isRtl)
    {
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Cairo:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    }
    else
    {
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    }
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'mdd-primary': '#005B82',
                        'mdd-accent': '#BCBEC0',
                        'mdd-text': '#3C3C3B',
                        'mdd-dark-bg': '#0F172A',
                        'mdd-dark-accent': '#33B5E5'
                    },
                    fontFamily: {
                        'arabic': ['Tajawal', 'Cairo', 'sans-serif'],
                        'english': ['Inter', 'Roboto', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/main.css">
    @if (isRtl)
    {
        <link rel="stylesheet" href="/css/rtl.css">
    }
    
    <!-- Additional head content -->
    @RenderSection("Head", required: false)
</head>
<body class="@(isRtl ? "font-arabic" : "font-english") bg-white dark:bg-mdd-dark-bg text-mdd-text dark:text-white transition-colors duration-300">
    <!-- Header -->
    @await Html.PartialAsync("~/Views/Partials/MddPlus/Header.cshtml")
    
    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @RenderBody()
    </main>
    
    <!-- Footer -->
    @await Html.PartialAsync("~/Views/Partials/MddPlus/Footer.cshtml")
    
    <!-- Scripts -->
    <script src="/js/theme-toggle.js"></script>
    <script src="/js/language-switcher.js"></script>
    <script src="/js/main.js"></script>
    
    @RenderSection("Scripts", required: false)
</body>
</html>
