@using Umbraco.Cms.Web.Common.PublishedModels;
@using MddPlus.Helpers;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = null;
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
    var isRtl = TranslationHelper.IsRTL(Context);
    var direction = TranslationHelper.GetDirection(Context);
    var fontFamily = TranslationHelper.GetFontFamily(Context);
    var isArabic = currentLanguage == "ar";
}
<!DOCTYPE html>
<html lang="@currentLanguage" dir="@direction">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#005B82">
    <meta name="msapplication-navbutton-color" content="#005B82">
    <meta name="apple-mobile-web-app-title" content="مدد بلس">
    <meta name="application-name" content="MDD Plus">
    <meta name="language" content="@currentLanguage">
    <meta name="direction" content="@direction">
    <title>@(ViewBag.Title ?? "مدد بلس - منصة التمويل الجماعي المتوافقة مع الشريعة")</title>
    <meta name="description" content="@(ViewBag.Description ?? "شركة سعودية مرخصة متخصصة في التمويل الجماعي القائم على الدين المتوافق مع الشريعة الإسلامية. نربط المستثمرين والمقترضين من خلال حلول مالية موثوقة.")">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">

    <!-- Fonts - Cairo as primary Arabic font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'mdd-primary': '#005B82',
                        'mdd-accent': '#BCBEC0',
                        'mdd-text': '#3C3C3B',
                        'mdd-dark-bg': '#0F172A',
                        'mdd-dark-accent': '#33B5E5'
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'english': ['Inter', 'sans-serif'],
                        'sans': ['Cairo', 'Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/advanced-features.css">
    @if (isRtl)
    {
        <link rel="stylesheet" href="/css/rtl.css">
    }

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Additional head content -->
    @RenderSection("Head", required: false)
</head>
<body class="@(isArabic ? "font-arabic" : "font-english") bg-white dark:bg-mdd-dark-bg text-mdd-text dark:text-white transition-all duration-300 antialiased" dir="@direction" style="font-family: @fontFamily;">
    <!-- Header -->
    @await Html.PartialAsync("~/Views/Partials/MddPlus/Header.cshtml")

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @RenderBody()
    </main>

    <!-- Footer -->
    @await Html.PartialAsync("~/Views/Partials/MddPlus/Footer.cshtml")

    <!-- Scripts -->
    <script src="/js/theme-toggle.js"></script>
    <script src="/js/advanced-language-switcher.js"></script>
    <script src="/js/advanced-features.js"></script>
    <script src="/js/main.js"></script>

    <!-- Progressive Web App Support -->
    <script>
        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Add viewport height fix for mobile browsers
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', setViewportHeight);
    </script>

    @RenderSection("Scripts", required: false)
</body>
</html>
