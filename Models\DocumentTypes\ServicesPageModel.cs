using Umbraco.Cms.Core.Models.PublishedContent;
using System.Collections.Generic;
using System.Linq;

namespace MddPlus.Models.DocumentTypes
{
    public class ServicesPageModel : BasePageModel
    {
        public ServicesPageModel(IPublishedContent content) : base(content) { }

        // Hero Section
        public string HeroTitleAr => this.Value<string>("heroTitleAr") ?? "خدماتنا المالية المتوافقة مع الشريعة";
        public string HeroTitleEn => this.Value<string>("heroTitleEn") ?? "Our Sharia-Compliant Financial Services";
        public string HeroSubtitleAr => this.Value<string>("heroSubtitleAr") ?? "نقدم مجموعة شاملة من الخدمات المالية المبتكرة";
        public string HeroSubtitleEn => this.Value<string>("heroSubtitleEn") ?? "We provide a comprehensive range of innovative financial services";
        
        // For Investors Section
        public string InvestorsTitleAr => this.Value<string>("investorsTitleAr") ?? "فرص استثمارية متنوعة وآمنة";
        public string InvestorsTitleEn => this.Value<string>("investorsTitleEn") ?? "Diverse and Safe Investment Opportunities";
        public string InvestorsDescriptionAr => this.Value<string>("investorsDescriptionAr") ?? "استثمر أموالك بطريقة آمنة ومتوافقة مع الشريعة";
        public string InvestorsDescriptionEn => this.Value<string>("investorsDescriptionEn") ?? "Invest your money safely and in compliance with Sharia";
        public IEnumerable<ServiceFeatureModel> InvestorsFeatures => this.Value<IEnumerable<IPublishedContent>>("investorsFeatures")?.Select(x => new ServiceFeatureModel(x)) ?? Enumerable.Empty<ServiceFeatureModel>();
        
        // For Borrowers Section
        public string BorrowersTitleAr => this.Value<string>("borrowersTitleAr") ?? "حلول تمويلية مرنة ومبتكرة";
        public string BorrowersTitleEn => this.Value<string>("borrowersTitleEn") ?? "Flexible and Innovative Financing Solutions";
        public string BorrowersDescriptionAr => this.Value<string>("borrowersDescriptionAr") ?? "احصل على التمويل اللازم لمشروعك بطريقة سريعة وآمنة";
        public string BorrowersDescriptionEn => this.Value<string>("borrowersDescriptionEn") ?? "Get the funding you need for your project quickly and safely";
        public IEnumerable<ServiceFeatureModel> BorrowersFeatures => this.Value<IEnumerable<IPublishedContent>>("borrowersFeatures")?.Select(x => new ServiceFeatureModel(x)) ?? Enumerable.Empty<ServiceFeatureModel>();
        
        // Service Categories
        public string ServiceCategoriesTitleAr => this.Value<string>("serviceCategoriesTitleAr") ?? "أنواع التمويل المتاحة";
        public string ServiceCategoriesTitleEn => this.Value<string>("serviceCategoriesTitleEn") ?? "Available Funding Types";
        public string ServiceCategoriesSubtitleAr => this.Value<string>("serviceCategoriesSubtitleAr") ?? "نقدم أنواع متعددة من التمويل";
        public string ServiceCategoriesSubtitleEn => this.Value<string>("serviceCategoriesSubtitleEn") ?? "We offer multiple types of funding";
        public IEnumerable<ServiceCategoryModel> ServiceCategories => this.Value<IEnumerable<IPublishedContent>>("serviceCategories")?.Select(x => new ServiceCategoryModel(x)) ?? Enumerable.Empty<ServiceCategoryModel>();
        
        // Process Steps
        public string ProcessTitleAr => this.Value<string>("processTitleAr") ?? "كيف تعمل العملية؟";
        public string ProcessTitleEn => this.Value<string>("processTitleEn") ?? "How Does the Process Work?";
        public string ProcessSubtitleAr => this.Value<string>("processSubtitleAr") ?? "عملية بسيطة وسريعة";
        public string ProcessSubtitleEn => this.Value<string>("processSubtitleEn") ?? "Simple and fast process";
        public IEnumerable<ProcessStepModel> ProcessSteps => this.Value<IEnumerable<IPublishedContent>>("processSteps")?.Select(x => new ProcessStepModel(x)) ?? Enumerable.Empty<ProcessStepModel>();
        
        // Helper Methods
        public string GetHeroTitle(string language = "ar")
        {
            return language == "ar" ? HeroTitleAr : HeroTitleEn;
        }
        
        public string GetHeroSubtitle(string language = "ar")
        {
            return language == "ar" ? HeroSubtitleAr : HeroSubtitleEn;
        }
        
        public string GetInvestorsTitle(string language = "ar")
        {
            return language == "ar" ? InvestorsTitleAr : InvestorsTitleEn;
        }
        
        public string GetInvestorsDescription(string language = "ar")
        {
            return language == "ar" ? InvestorsDescriptionAr : InvestorsDescriptionEn;
        }
        
        public string GetBorrowersTitle(string language = "ar")
        {
            return language == "ar" ? BorrowersTitleAr : BorrowersTitleEn;
        }
        
        public string GetBorrowersDescription(string language = "ar")
        {
            return language == "ar" ? BorrowersDescriptionAr : BorrowersDescriptionEn;
        }
    }
}
