@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
    var isRtl = currentCulture.TextInfo.IsRightToLeft;
    var isArabic = currentCulture.TwoLetterISOLanguageName == "ar";
}

<header class="bg-white dark:bg-mdd-dark-bg shadow-lg border-b border-mdd-accent dark:border-mdd-dark-accent transition-colors duration-300">
    <!-- Top Bar -->
    <div class="bg-mdd-primary dark:bg-mdd-dark-bg">
        <div class="container mx-auto px-4 py-2">
            <div class="flex justify-between items-center text-white text-sm">
                <div class="flex items-center space-x-4 @(isRtl ? "space-x-reverse" : "")">
                    <span>@(isArabic ? "مرخصة من هيئة السوق المالية" : "Licensed by CMA")</span>
                    <span class="hidden md:inline">|</span>
                    <span class="hidden md:inline">@(isArabic ? "سجل تجاري: **********" : "CR: **********")</span>
                </div>
                <div class="flex items-center space-x-3 @(isRtl ? "space-x-reverse" : "")">
                    <!-- Language Switcher -->
                    <div class="relative">
                        <button id="language-toggle" class="flex items-center space-x-1 @(isRtl ? "space-x-reverse" : "") hover:text-mdd-dark-accent transition-colors">
                            <span>@(isArabic ? "EN" : "العربية")</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-1 rounded-full hover:bg-white/10 transition-colors">
                        <svg id="theme-toggle-dark-icon" class="hidden w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                        <svg id="theme-toggle-light-icon" class="hidden w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Navigation -->
    <nav class="container mx-auto px-4 py-4">
        <div class="flex justify-between items-center">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="/" class="flex items-center space-x-3 @(isRtl ? "space-x-reverse" : "")">
                    <img src="/images/mdd-plus-logo.svg" alt="MDD Plus" class="h-12 w-auto">
                    <div class="@(isRtl ? "text-right" : "text-left")">
                        <h1 class="text-xl font-bold text-mdd-primary dark:text-white">
                            @(isArabic ? "مدد بلس" : "MDD Plus")
                        </h1>
                        <p class="text-xs text-mdd-accent dark:text-mdd-dark-accent">
                            @(isArabic ? "التمويل الجماعي المتوافق مع الشريعة" : "Sharia-Compliant Crowdfunding")
                        </p>
                    </div>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-8 @(isRtl ? "space-x-reverse" : "")">
                <a href="/" class="nav-link">@(isArabic ? "الرئيسية" : "Home")</a>
                <a href="/about" class="nav-link">@(isArabic ? "من نحن" : "About Us")</a>
                <a href="/services" class="nav-link">@(isArabic ? "خدماتنا" : "Services")</a>
                <a href="/investors" class="nav-link">@(isArabic ? "المستثمرون" : "Investors")</a>
                <a href="/borrowers" class="nav-link">@(isArabic ? "المقترضون" : "Borrowers")</a>
                <a href="/faq" class="nav-link">@(isArabic ? "الأسئلة الشائعة" : "FAQ")</a>
                <a href="/contact" class="nav-link">@(isArabic ? "اتصل بنا" : "Contact")</a>
            </div>
            
            <!-- CTA Buttons -->
            <div class="hidden lg:flex items-center space-x-4 @(isRtl ? "space-x-reverse" : "")">
                <a href="/login" class="btn-secondary">
                    @(isArabic ? "تسجيل الدخول" : "Login")
                </a>
                <a href="/register" class="btn-primary">
                    @(isArabic ? "إنشاء حساب" : "Sign Up")
                </a>
            </div>
            
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="hidden lg:hidden mt-4 pb-4 border-t border-mdd-accent dark:border-mdd-dark-accent">
            <div class="flex flex-col space-y-3 mt-4">
                <a href="/" class="mobile-nav-link">@(isArabic ? "الرئيسية" : "Home")</a>
                <a href="/about" class="mobile-nav-link">@(isArabic ? "من نحن" : "About Us")</a>
                <a href="/services" class="mobile-nav-link">@(isArabic ? "خدماتنا" : "Services")</a>
                <a href="/investors" class="mobile-nav-link">@(isArabic ? "المستثمرون" : "Investors")</a>
                <a href="/borrowers" class="mobile-nav-link">@(isArabic ? "المقترضون" : "Borrowers")</a>
                <a href="/faq" class="mobile-nav-link">@(isArabic ? "الأسئلة الشائعة" : "FAQ")</a>
                <a href="/contact" class="mobile-nav-link">@(isArabic ? "اتصل بنا" : "Contact")</a>
                
                <div class="flex flex-col space-y-2 pt-4 border-t border-mdd-accent dark:border-mdd-dark-accent">
                    <a href="/login" class="btn-secondary text-center">
                        @(isArabic ? "تسجيل الدخول" : "Login")
                    </a>
                    <a href="/register" class="btn-primary text-center">
                        @(isArabic ? "إنشاء حساب" : "Sign Up")
                    </a>
                </div>
            </div>
        </div>
    </nav>
</header>
