/* Admin Panel Styles for MDD Plus */

/* Base Styles */
.admin-nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
}

.admin-nav-link.active {
    @apply bg-mdd-primary text-white hover:bg-mdd-primary;
}

.admin-nav-link svg {
    @apply ml-3 w-5 h-5;
}

.admin-nav-group {
    @apply mb-4;
}

.admin-nav-group-title {
    @apply flex items-center px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.admin-nav-group-title svg {
    @apply ml-2 w-4 h-4;
}

.admin-nav-group-items {
    @apply space-y-1 mr-4;
}

.admin-nav-sublink {
    @apply block px-3 py-2 text-sm text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200;
}

.admin-nav-sublink.active {
    @apply bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

/* Cards */
.admin-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6;
}

.admin-card-header {
    @apply flex items-center justify-between mb-4 pb-4 border-b border-gray-200 dark:border-gray-700;
}

.admin-card-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.admin-card-subtitle {
    @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Stats Cards */
.stat-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200;
}

.stat-card-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl mb-4;
}

.stat-card-value {
    @apply text-2xl font-bold text-gray-900 dark:text-white mb-1;
}

.stat-card-label {
    @apply text-sm text-gray-500 dark:text-gray-400;
}

.stat-card-change {
    @apply text-xs font-medium;
}

.stat-card-change.positive {
    @apply text-green-600 dark:text-green-400;
}

.stat-card-change.negative {
    @apply text-red-600 dark:text-red-400;
}

/* Forms */
.admin-form {
    @apply space-y-6;
}

.admin-form-group {
    @apply space-y-2;
}

.admin-form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.admin-form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-mdd-primary focus:border-mdd-primary;
}

.admin-form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-mdd-primary focus:border-mdd-primary resize-vertical;
}

.admin-form-select {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-mdd-primary focus:border-mdd-primary;
}

.admin-form-checkbox {
    @apply h-4 w-4 text-mdd-primary focus:ring-mdd-primary border-gray-300 dark:border-gray-600 rounded;
}

.admin-form-error {
    @apply text-sm text-red-600 dark:text-red-400;
}

.admin-form-help {
    @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Buttons */
.btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-mdd-primary hover:bg-mdd-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mdd-primary transition-colors duration-200;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mdd-primary transition-colors duration-200;
}

.btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200;
}

.btn-success {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200;
}

.btn-icon {
    @apply inline-flex items-center justify-center w-8 h-8 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-icon-primary {
    @apply btn-icon text-white bg-mdd-primary hover:bg-mdd-secondary focus:ring-mdd-primary;
}

.btn-icon-secondary {
    @apply btn-icon text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-mdd-primary;
}

.btn-icon-danger {
    @apply btn-icon text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

/* Tables */
.admin-table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.admin-table thead {
    @apply bg-gray-50 dark:bg-gray-800;
}

.admin-table th {
    @apply px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.admin-table tbody {
    @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
}

.admin-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white;
}

.admin-table-actions {
    @apply flex items-center space-x-2 space-x-reverse;
}

/* Badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply badge bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200;
}

.badge-danger {
    @apply badge bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}

.badge-info {
    @apply badge bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200;
}

.badge-gray {
    @apply badge bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
}

/* Loading Spinner */
.loading-spinner {
    @apply inline-block w-6 h-6 border-2 border-gray-300 border-t-mdd-primary rounded-full animate-spin;
}

/* Responsive Grid */
.admin-grid {
    @apply grid gap-6;
}

.admin-grid-1 {
    @apply admin-grid grid-cols-1;
}

.admin-grid-2 {
    @apply admin-grid grid-cols-1 md:grid-cols-2;
}

.admin-grid-3 {
    @apply admin-grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.admin-grid-4 {
    @apply admin-grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
}

/* Drag and Drop */
.sortable-item {
    @apply cursor-move transition-all duration-200;
}

.sortable-item:hover {
    @apply shadow-md;
}

.sortable-item.dragging {
    @apply opacity-50 transform rotate-2;
}

.sortable-placeholder {
    @apply border-2 border-dashed border-mdd-primary bg-blue-50 dark:bg-blue-900 rounded-lg;
}

/* Color Picker */
.color-picker {
    @apply grid grid-cols-6 gap-2;
}

.color-option {
    @apply w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600 cursor-pointer hover:scale-110 transition-transform duration-200;
}

.color-option.selected {
    @apply border-gray-900 dark:border-white ring-2 ring-offset-2 ring-mdd-primary;
}

/* Icon Picker */
.icon-picker {
    @apply grid grid-cols-8 gap-2 max-h-48 overflow-y-auto;
}

.icon-option {
    @apply w-10 h-10 flex items-center justify-center text-lg border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
}

.icon-option.selected {
    @apply bg-mdd-primary text-white border-mdd-primary;
}

/* Media Gallery */
.media-gallery {
    @apply grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4;
}

.media-item {
    @apply relative group cursor-pointer rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-mdd-primary transition-colors duration-200;
}

.media-item img {
    @apply w-full h-24 object-cover;
}

.media-item-overlay {
    @apply absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200;
}

.media-item-actions {
    @apply flex space-x-2 space-x-reverse;
}

/* File Upload */
.file-upload-area {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-mdd-primary transition-colors duration-200;
}

.file-upload-area.dragover {
    @apply border-mdd-primary bg-blue-50 dark:bg-blue-900;
}

/* Progress Bar */
.progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-bar-fill {
    @apply bg-mdd-primary h-2 rounded-full transition-all duration-300;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.slide-out-right {
    animation: slideOutRight 0.3s ease-out;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .admin-card {
        @apply p-4;
    }
    
    .admin-table {
        @apply text-sm;
    }
    
    .admin-table th,
    .admin-table td {
        @apply px-3 py-2;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-danger,
    .btn-success {
        @apply text-xs px-3 py-2;
    }
    
    .stat-card {
        @apply p-4;
    }
    
    .stat-card-value {
        @apply text-xl;
    }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
    .admin-card {
        @apply bg-gray-800 border-gray-700;
    }
    
    .admin-form-input,
    .admin-form-textarea,
    .admin-form-select {
        @apply bg-gray-700 border-gray-600 text-white;
    }
    
    .admin-table thead {
        @apply bg-gray-800;
    }
    
    .admin-table tbody {
        @apply bg-gray-900;
    }
}
