using Umbraco.Cms.Core.Composing;
using Umbraco.Cms.Core.DependencyInjection;
using Umbraco.Extensions;
using MddPlus.Models;

namespace MddPlus.Startup
{
    public class UmbracoStartupComposer : IComposer
    {
        public void Compose(IUmbracoBuilder builder)
        {
            // Register custom services
            builder.Services.AddSingleton<LanguageService>();

            // Register custom models
            // Note: PublishedContentModelCollectionBuilder is deprecated in newer versions
            // Models will be auto-discovered
        }
    }
}
