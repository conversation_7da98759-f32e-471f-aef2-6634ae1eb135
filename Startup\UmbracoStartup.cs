using Umbraco.Cms.Core.Composing;
using Umbraco.Cms.Core.DependencyInjection;
using Umbraco.Extensions;
using MddPlus.Models;

namespace MddPlus.Startup
{
    public class UmbracoStartupComposer : IComposer
    {
        public void Compose(IUmbracoBuilder builder)
        {
            // Register custom services
            builder.Services.AddSingleton<LanguageService>();
            
            // Register custom models
            builder.WithCollectionBuilder<PublishedContentModelCollectionBuilder>()
                .Add<Models.DocumentTypes.BasePageModel>()
                .Add<Models.DocumentTypes.HomePageModel>()
                .Add<Models.DocumentTypes.ServicesPageModel>()
                .Add<Models.DocumentTypes.StatisticItemModel>()
                .Add<Models.DocumentTypes.FeatureItemModel>()
                .Add<Models.DocumentTypes.ServiceCategoryModel>()
                .Add<Models.DocumentTypes.ServiceFeatureModel>()
                .Add<Models.DocumentTypes.ProcessStepModel>();
        }
    }
}
