using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Umbraco.Cms.Core.Web;
using Umbraco.Cms.Web.Common.Controllers;
using MddPlus.Models.DocumentTypes;

namespace MddPlus.Controllers
{
    public class HomePageController : RenderController
    {
        public HomePageController(ILogger<RenderController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor) 
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
        }

        public override IActionResult Index()
        {
            var model = new HomePageModel(CurrentPage);
            return CurrentTemplate(model);
        }
    }

    public class ServicesPageController : RenderController
    {
        public ServicesPageController(ILogger<RenderController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor) 
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
        }

        public override IActionResult Index()
        {
            var model = new ServicesPageModel(CurrentPage);
            return CurrentTemplate(model);
        }
    }

    public class BasePageController : RenderController
    {
        public BasePageController(ILogger<RenderController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor) 
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
        }

        public override IActionResult Index()
        {
            var model = new BasePageModel(CurrentPage);
            return CurrentTemplate(model);
        }
    }
}
