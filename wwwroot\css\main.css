/* MDD Plus - Main Stylesheet */

/* CSS Variables for Theme Colors */
:root {
  --mdd-primary: #005B82;
  --mdd-accent: #BCBEC0;
  --mdd-text: #3C3C3B;
  --mdd-bg-light: #FFFFFF;
  --mdd-bg-dark: #0F172A;
  --mdd-accent-dark: #33B5E5;
}

/* Dark mode variables */
.dark {
  --mdd-bg: var(--mdd-bg-dark);
  --mdd-text: #FFFFFF;
  --mdd-accent: var(--mdd-accent-dark);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--mdd-text);
  background-color: var(--mdd-bg-light);
  transition: all 0.3s ease;
}

/* Arabic Font */
body.font-arabic {
  font-family: 'Ta<PERSON>wal', 'Cairo', sans-serif;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Button Styles */
.btn-primary {
  @apply inline-flex items-center justify-center px-6 py-3 bg-mdd-primary hover:bg-mdd-primary/90 text-white font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-6 py-3 bg-transparent border-2 border-mdd-primary text-mdd-primary hover:bg-mdd-primary hover:text-white font-medium rounded-lg transition-all duration-300;
}

.btn-accent {
  @apply inline-flex items-center justify-center px-6 py-3 bg-mdd-accent hover:bg-mdd-accent/90 text-mdd-text font-medium rounded-lg transition-all duration-300;
}

/* Dark mode button styles */
.dark .btn-secondary {
  @apply border-mdd-dark-accent text-mdd-dark-accent hover:bg-mdd-dark-accent hover:text-mdd-dark-bg;
}

/* Navigation Styles */
.nav-link {
  @apply text-mdd-text dark:text-white hover:text-mdd-primary dark:hover:text-mdd-dark-accent font-medium transition-colors duration-300 relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--mdd-primary);
  transition: width 0.3s ease;
}

.dark .nav-link::after {
  background-color: var(--mdd-accent-dark);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.mobile-nav-link {
  @apply block py-2 px-4 text-mdd-text dark:text-white hover:text-mdd-primary dark:hover:text-mdd-dark-accent hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-300;
}

/* Social Links */
.social-link {
  @apply inline-flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full transition-all duration-300 hover:scale-110;
}

/* Footer Links */
.footer-link {
  @apply text-gray-300 hover:text-white transition-colors duration-300;
}

/* Card Styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700;
}

.card-hover {
  @apply transform hover:-translate-y-2 hover:scale-105;
}

/* Hero Section */
.hero-gradient {
  background: linear-gradient(135deg, var(--mdd-primary) 0%, #007BA7 100%);
}

.dark .hero-gradient {
  background: linear-gradient(135deg, var(--mdd-bg-dark) 0%, #1E293B 100%);
}

/* Feature Icons */
.feature-icon {
  @apply w-16 h-16 bg-mdd-primary/10 dark:bg-mdd-dark-accent/10 rounded-full flex items-center justify-center text-mdd-primary dark:text-mdd-dark-accent mb-4;
}

/* Stats Counter */
.stat-number {
  @apply text-4xl font-bold text-mdd-primary dark:text-mdd-dark-accent;
}

/* Form Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-mdd-primary dark:focus:ring-mdd-dark-accent focus:border-transparent bg-white dark:bg-gray-800 text-mdd-text dark:text-white transition-all duration-300;
}

.form-label {
  @apply block text-sm font-medium text-mdd-text dark:text-white mb-2;
}

/* Loading Animation */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-mdd-primary dark:border-mdd-dark-accent;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  
  .btn-primary,
  .btn-secondary,
  .btn-accent {
    @apply px-4 py-2 text-sm;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-mdd-primary dark:ring-mdd-dark-accent ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: var(--mdd-primary);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--mdd-accent-dark);
}

::-webkit-scrollbar-thumb:hover {
  background: #004666;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #29A3D1;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}
