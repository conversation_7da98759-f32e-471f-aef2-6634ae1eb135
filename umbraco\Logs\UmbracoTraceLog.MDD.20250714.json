{"@t":"2025-07-14T13:43:04.4256252Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.4325315Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9815528Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9839689Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9862907Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9865462Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9866752Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9867861Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9868852Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9869787Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9870648Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9872368Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9873634Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9874869Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9876012Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:04.9876389Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:05.0616365Z","@mt":"Now listening on: {address}","address":"https://localhost:44301","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:05.0618396Z","@mt":"Now listening on: {address}","address":"http://localhost:16385","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:05.0619159Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:05.0619328Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:43:05.0619508Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\mdd_plus","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":1,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:44:31.1963161Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2011330Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2198591Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2200227Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2203384Z","@mt":"Skipping {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2204075Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.2206446Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.7111387Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.7112917Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.7113943Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.7211309Z","@mt":"Database configuration status: Started","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.8956331Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUser\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userDisabled\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userDisabled\" DEFAULT ('0')\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_key\" DEFAULT (NEWID())\r\n, \"userNoConsole\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userNoConsole\" DEFAULT ('0')\r\n, \"userName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLogin\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userPassword\" TEXT COLLATE NOCASE NOT NULL\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"userEmail\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLanguage\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"failedLoginAttempts\" INTEGER NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"invitedDate\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_updateDate\" DEFAULT (DATE())\r\n, \"kind\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_kind\" DEFAULT ('0')\r\n, \"avatar\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_user UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.8986743Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUser_userKey\" ON \"umbracoUser\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.8990124Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUser_userLogin\" ON \"umbracoUser\" (\"userLogin\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9006735Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9380236Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9388942Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9438236Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_uniqueId\" DEFAULT (NEWID())\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"level\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, \"trashed\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoNode_trashed\" DEFAULT ('0')\r\n, \"nodeUser\" INTEGER NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"nodeObjectType\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoNode_umbracoNode_id FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoNode_umbracoUser_id FOREIGN KEY (\"nodeUser\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9451774Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoNode_UniqueId\" ON \"umbracoNode\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9455415Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_parentId_nodeObjectType\" ON \"umbracoNode\" (\"parentID\",\"nodeObjectType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9458487Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_Level\" ON \"umbracoNode\" (\"level\",\"parentId\",\"sortOrder\",\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9472743Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_Path\" ON \"umbracoNode\" (\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9479977Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType_trashed_sorted\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\",\"sortOrder\",\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9483687Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_Trashed\" ON \"umbracoNode\" (\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9487458Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9490878Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9821060Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9822149Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9827268Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsContentType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"thumbnail\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsContentType_thumbnail\" DEFAULT ('folder.png')\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"listView\" TEXT NULL\r\n, \"isElement\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_isElement\" DEFAULT ('0')\r\n, \"allowAtRoot\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_allowAtRoot\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_variations\" DEFAULT ('1')\r\n, CONSTRAINT PK_cmsContentType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsContentType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9831950Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsContentType\" ON \"cmsContentType\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9834385Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsContentType_icon\" ON \"cmsContentType\" (\"icon\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:31.9835683Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0045675Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0047122Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0052361Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsTemplate\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_cmsTemplate UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsTemplate_umbracoNode FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0056759Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsTemplate_nodeId\" ON \"cmsTemplate\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0060723Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0061485Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0061846Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0066561Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoContent\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoContent PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoContent_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContent_cmsContentType_NodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"NodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0074055Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0074766Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0075144Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0081331Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoContentVersion\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"versionDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoContentVersion_versionDate\" DEFAULT (DATE())\r\n, \"userId\" INTEGER NULL\r\n, \"current\" INTEGER NOT NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"preventCleanup\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoContentVersion_preventCleanup\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoContentVersion UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersion_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentVersion_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0089137Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_NodeId\" ON \"umbracoContentVersion\" (\"nodeId\",\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0093174Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_Current\" ON \"umbracoContentVersion\" (\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0096150Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0096765Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0097102Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0102428Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoMediaVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoMediaVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoMediaVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0106148Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoMediaVersion\" ON \"umbracoMediaVersion\" (\"id\",\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0109018Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0109549Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0109863Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0114718Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDocument\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocument PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDocument_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0118177Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoDocument_Published\" ON \"umbracoDocument\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0121296Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0122965Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0123693Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0130684Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsDocumentType\r\n(\r\n \"contentTypeNodeId\" INTEGER NOT NULL\r\n, \"templateNodeId\" INTEGER NOT NULL\r\n, \"IsDefault\" INTEGER NOT NULL CONSTRAINT \"DF_cmsDocumentType_IsDefault\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsDocumentType PRIMARY KEY (\"contentTypeNodeId\", \"templateNodeId\")\r\n, CONSTRAINT FK_cmsDocumentType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsDocumentType_umbracoNode_id FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsDocumentType_cmsTemplate_nodeId FOREIGN KEY (\"templateNodeId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0138193Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0140384Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0140842Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0149772Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDataType\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"propertyEditorAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"propertyEditorUiAlias\" TEXT COLLATE NOCASE NULL\r\n, \"dbType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"config\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDataType PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDataType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0154147Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0428667Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0429814Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0441802Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsDictionary\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"id\" TEXT NOT NULL\r\n, \"parent\" TEXT NULL\r\n, \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsDictionary UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsDictionary_cmsDictionary_id FOREIGN KEY (\"parent\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0443791Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_id\" ON \"cmsDictionary\" (\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0444863Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsDictionary_Parent\" ON \"cmsDictionary\" (\"parent\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0445656Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_key\" ON \"cmsDictionary\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0446447Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0446604Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0446727Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0448914Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoLanguage\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageISOCode\" TEXT COLLATE NOCASE NULL\r\n, \"languageCultureName\" TEXT COLLATE NOCASE NULL\r\n, \"isDefaultVariantLang\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_isDefaultVariantLang\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_mandatory\" DEFAULT ('0')\r\n, \"fallbackLanguageId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoLanguage UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLanguage_umbracoLanguage_id FOREIGN KEY (\"fallbackLanguageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0450132Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoLanguage_languageISOCode\" ON \"umbracoLanguage\" (\"languageISOCode\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0451919Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoLanguage_fallbackLanguageId\" ON \"umbracoLanguage\" (\"fallbackLanguageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0453792Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0559326Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0560233Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0565224Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsLanguageText\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"UniqueId\" TEXT NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsLanguageText UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsLanguageText_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_cmsLanguageText_cmsDictionary_id FOREIGN KEY (\"UniqueId\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0572466Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsLanguageText_languageId\" ON \"cmsLanguageText\" (\"languageId\",\"UniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0575690Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0576190Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0577707Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0582691Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDomain\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"domainDefaultLanguage\" INTEGER NULL\r\n, \"domainRootStructureID\" INTEGER NULL\r\n, \"domainName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDomain UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDomain_umbracoNode_id FOREIGN KEY (\"domainRootStructureID\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0587400Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0588093Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0588415Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0605505Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NULL\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"entityType\" TEXT COLLATE NOCASE NULL\r\n, \"Datestamp\" TEXT NOT NULL CONSTRAINT \"DF_umbracoLog_Datestamp\" DEFAULT (DATE())\r\n, \"logHeader\" TEXT COLLATE NOCASE NOT NULL\r\n, \"logComment\" TEXT COLLATE NOCASE NULL\r\n, \"parameters\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoLog UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLog_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0607773Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoLog\" ON \"umbracoLog\" (\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0609021Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp\" ON \"umbracoLog\" (\"Datestamp\",\"userId\",\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0610451Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp_logheader\" ON \"umbracoLog\" (\"Datestamp\",\"logHeader\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0611674Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0611895Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0612024Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0614789Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsMemberType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"propertytypeId\" INTEGER NOT NULL\r\n, \"memberCanEdit\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_memberCanEdit\" DEFAULT ('0')\r\n, \"viewOnProfile\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_viewOnProfile\" DEFAULT ('0')\r\n, \"isSensitive\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_isSensitive\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsMemberType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsMemberType_umbracoNode_id FOREIGN KEY (\"NodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsMemberType_cmsContentType_nodeId FOREIGN KEY (\"NodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0616328Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0616611Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0616759Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0621883Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsMember\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"Email\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Email\" DEFAULT ('''')\r\n, \"LoginName\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_LoginName\" DEFAULT ('''')\r\n, \"Password\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Password\" DEFAULT ('''')\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"failedPasswordAttempts\" INTEGER NULL\r\n, \"isLockedOut\" INTEGER NULL CONSTRAINT \"DF_cmsMember_isLockedOut\" DEFAULT ('0')\r\n, \"isApproved\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMember_isApproved\" DEFAULT ('1')\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, CONSTRAINT PK_cmsMember PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_cmsMember_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0623901Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsMember_LoginName\" ON \"cmsMember\" (\"LoginName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0625110Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0625319Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0625466Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0627346Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsMember2MemberGroup\r\n(\r\n \"Member\" INTEGER NOT NULL\r\n, \"MemberGroup\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsMember2MemberGroup PRIMARY KEY (\"Member\", \"MemberGroup\")\r\n, CONSTRAINT FK_cmsMember2MemberGroup_cmsMember_nodeId FOREIGN KEY (\"Member\") REFERENCES \"cmsMember\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsMember2MemberGroup_umbracoNode_id FOREIGN KEY (\"MemberGroup\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0630147Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0630699Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0631059Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0636688Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsPropertyTypeGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_uniqueID\" DEFAULT (NEWID())\r\n, \"contenttypeNodeId\" INTEGER NOT NULL\r\n, \"type\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_type\" DEFAULT ('0')\r\n, \"text\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortorder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsPropertyTypeGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyTypeGroup_cmsContentType_nodeId FOREIGN KEY (\"contenttypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0641854Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeGroupUniqueID\" ON \"cmsPropertyTypeGroup\" (\"uniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0645387Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0814495Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0815761Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0830583Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsPropertyType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"dataTypeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, \"propertyTypeGroupId\" INTEGER NULL\r\n, \"Alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Name\" TEXT COLLATE NOCASE NULL\r\n, \"sortOrder\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_sortOrder\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_mandatory\" DEFAULT ('0')\r\n, \"mandatoryMessage\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExp\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExpMessage\" TEXT COLLATE NOCASE NULL\r\n, \"Description\" TEXT COLLATE NOCASE NULL\r\n, \"labelOnTop\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_labelOnTop\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_variations\" DEFAULT ('1')\r\n, \"UniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyType_UniqueID\" DEFAULT (NEWID())\r\n, CONSTRAINT PK_cmsPropertyType UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyType_umbracoDataType_nodeId FOREIGN KEY (\"dataTypeId\") REFERENCES \"umbracoDataType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsPropertyTypeGroup_id FOREIGN KEY (\"propertyTypeGroupId\") REFERENCES \"cmsPropertyTypeGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0840169Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsPropertyTypeAlias\" ON \"cmsPropertyType\" (\"Alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0854463Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeUniqueID\" ON \"cmsPropertyType\" (\"UniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.0858621Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1090076Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1091800Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1108114Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoPropertyData\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"segment\" TEXT COLLATE NOCASE NULL\r\n, \"intValue\" INTEGER NULL\r\n, \"decimalValue\" TEXT NULL\r\n, \"dateValue\" TEXT NULL\r\n, \"varcharValue\" TEXT COLLATE NOCASE NULL\r\n, \"textValue\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoPropertyData UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoPropertyData_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_cmsPropertyType_id FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1115222Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoPropertyData_VersionId\" ON \"umbracoPropertyData\" (\"versionId\",\"propertyTypeId\",\"languageId\",\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1118516Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_PropertyTypeId\" ON \"umbracoPropertyData\" (\"propertyTypeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1120742Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_LanguageId\" ON \"umbracoPropertyData\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1124246Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_Segment\" ON \"umbracoPropertyData\" (\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1126566Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1126987Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1128257Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1135013Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoRelationType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"typeUniqueId\" TEXT NOT NULL\r\n, \"dual\" INTEGER NOT NULL\r\n, \"parentObjectType\" TEXT NULL\r\n, \"childObjectType\" TEXT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isDependency\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoRelationType_isDependency\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoRelationType UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1139132Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_UniqueId\" ON \"umbracoRelationType\" (\"typeUniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1141857Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_name\" ON \"umbracoRelationType\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1144725Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_alias\" ON \"umbracoRelationType\" (\"alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1146970Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1241242Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1242429Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1248740Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoRelation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"childId\" INTEGER NOT NULL\r\n, \"relType\" INTEGER NOT NULL\r\n, \"datetime\" TEXT NOT NULL CONSTRAINT \"DF_umbracoRelation_datetime\" DEFAULT (DATE())\r\n, \"comment\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRelation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoRelation_umbracoNode FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoNode1 FOREIGN KEY (\"childId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoRelationType_id FOREIGN KEY (\"relType\") REFERENCES \"umbracoRelationType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1252795Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelation_parentChildType\" ON \"umbracoRelation\" (\"parentId\",\"childId\",\"relType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1255983Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1256484Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1256796Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1267199Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsTags\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"tag\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsTags UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsTags_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1273420Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsTags_languageId_group\" ON \"cmsTags\" (\"languageId\",\"group\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1280704Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsTags_LanguageId\" ON \"cmsTags\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1286421Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_cmsTags\" ON \"cmsTags\" (\"group\",\"tag\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1289192Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1289612Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1289896Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1295176Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsTagRelationship\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"tagId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsTagRelationship PRIMARY KEY (\"nodeId\", \"propertyTypeId\", \"tagId\")\r\n, CONSTRAINT FK_cmsTagRelationship_cmsContent FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsTags_id FOREIGN KEY (\"tagId\") REFERENCES \"cmsTags\" (\"id\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsPropertyType FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1298406Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsTagRelationship_tagId_nodeId\" ON \"cmsTagRelationship\" (\"tagId\",\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1300453Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1300817Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1301096Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1304407Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsContentType2ContentType\r\n(\r\n \"parentContentTypeId\" INTEGER NOT NULL\r\n, \"childContentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsContentType2ContentType PRIMARY KEY (\"parentContentTypeId\", \"childContentTypeId\")\r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_parent FOREIGN KEY (\"parentContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_child FOREIGN KEY (\"childContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1307751Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1308141Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1308470Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1318218Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsContentTypeAllowedContentType\r\n(\r\n \"Id\" INTEGER NOT NULL\r\n, \"AllowedId\" INTEGER NOT NULL\r\n, \"SortOrder\" INTEGER NOT NULL CONSTRAINT df_cmsContentTypeAllowedContentType_sortOrder DEFAULT ('0')\r\n, CONSTRAINT PK_cmsContentTypeAllowedContentType PRIMARY KEY (\"Id\", \"AllowedId\")\r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType FOREIGN KEY (\"Id\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType1 FOREIGN KEY (\"AllowedId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1321655Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1440789Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1442555Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1454142Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUser2NodeNotify\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2NodeNotify PRIMARY KEY (\"userId\", \"nodeId\", \"action\")\r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1459136Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1459785Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1460860Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1466756Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUser2ClientId\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"clientId\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2ClientId PRIMARY KEY (\"userId\", \"clientId\")\r\n, CONSTRAINT FK_umbracoUser2ClientId_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1471945Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1472751Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1473081Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1478849Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoServer\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"address\" TEXT COLLATE NOCASE NOT NULL\r\n, \"computerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"registeredDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoServer_registeredDate\" DEFAULT (DATE())\r\n, \"lastNotifiedDate\" TEXT NOT NULL\r\n, \"isActive\" INTEGER NOT NULL\r\n, \"isSchedulingPublisher\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoServer UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1484737Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_computerName\" ON \"umbracoServer\" (\"computerName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1490679Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoServer_isActive\" ON \"umbracoServer\" (\"isActive\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1496142Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1496761Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1498278Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1505289Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoAccess\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"loginNodeId\" INTEGER NOT NULL\r\n, \"noAccessNodeId\" INTEGER NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccess PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id1 FOREIGN KEY (\"loginNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id2 FOREIGN KEY (\"noAccessNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1509649Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccess_nodeId\" ON \"umbracoAccess\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1512586Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1562173Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1562998Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1576532Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoAccessRule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"accessId\" TEXT NOT NULL\r\n, \"ruleValue\" TEXT COLLATE NOCASE NOT NULL\r\n, \"ruleType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccessRule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccessRule_umbracoAccess_id FOREIGN KEY (\"accessId\") REFERENCES \"umbracoAccess\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1583585Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccessRule\" ON \"umbracoAccessRule\" (\"ruleValue\",\"ruleType\",\"accessId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1588524Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1589283Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1589673Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1606213Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoCacheInstruction\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"utcStamp\" TEXT NOT NULL\r\n, \"jsonInstruction\" TEXT COLLATE NOCASE NOT NULL\r\n, \"originated\" TEXT COLLATE NOCASE NOT NULL\r\n, \"instructionCount\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoCacheInstruction_instructionCount\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoCacheInstruction UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1611520Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1612195Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1612612Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1621735Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoExternalLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"loginProvider\" TEXT COLLATE NOCASE NOT NULL\r\n, \"providerKey\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLogin_createDate\" DEFAULT (DATE())\r\n, \"userData\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoExternalLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1633977Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_userOrMemberKey\" ON \"umbracoExternalLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1639597Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLogin_LoginProvider\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1643732Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_ProviderKey\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"providerKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1647572Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1648456Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1648931Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1660995Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoExternalLoginToken\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"externalLoginId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLoginToken_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoExternalLoginToken UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoExternalLoginToken_umbracoExternalLogin_id FOREIGN KEY (\"externalLoginId\") REFERENCES \"umbracoExternalLogin\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1667164Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLoginToken_Name\" ON \"umbracoExternalLoginToken\" (\"externalLoginId\",\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1671843Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1672701Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1673170Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1681773Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoTwoFactorLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"providerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"secret\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoTwoFactorLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1700877Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoTwoFactorLogin_userOrMemberKey\" ON \"umbracoTwoFactorLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1706608Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoTwoFactorLogin_ProviderName\" ON \"umbracoTwoFactorLogin\" (\"providerName\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1711656Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1712377Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1713058Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1724338Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoRedirectUrl\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"contentKey\" TEXT NOT NULL\r\n, \"createDateUtc\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"culture\" TEXT COLLATE NOCASE NULL\r\n, \"urlHash\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRedirectUrl PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoRedirectUrl_umbracoNode_uniqueID FOREIGN KEY (\"contentKey\") REFERENCES \"umbracoNode\" (\"uniqueID\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1730687Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoRedirectUrl_culture_hash\" ON \"umbracoRedirectUrl\" (\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1742073Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRedirectUrl\" ON \"umbracoRedirectUrl\" (\"urlHash\",\"contentKey\",\"culture\",\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1750041Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1750829Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1751511Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1772891Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoLock\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"value\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLock PRIMARY KEY (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1778448Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1888523Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1889993Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1911207Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_key\" DEFAULT (NEWID())\r\n, \"userGroupAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupDefaultPermissions\" TEXT COLLATE NOCASE NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_updateDate\" DEFAULT (DATE())\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"hasAccessToAllLanguages\" INTEGER NOT NULL\r\n, \"startContentId\" INTEGER NULL\r\n, \"startMediaId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoUserGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_startContentId_umbracoNode_id FOREIGN KEY (\"startContentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_startMediaId_umbracoNode_id FOREIGN KEY (\"startMediaId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1925968Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupKey\" ON \"umbracoUserGroup\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1935835Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupAlias\" ON \"umbracoUserGroup\" (\"userGroupAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1952329Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupName\" ON \"umbracoUserGroup\" (\"userGroupName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.1958043Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2212402Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2214581Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2235395Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUser2UserGroup\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"userGroupId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_user2userGroup PRIMARY KEY (\"userId\", \"userGroupId\")\r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2266545Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2456283Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2458466Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2476689Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserGroup2App\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"app\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2App PRIMARY KEY (\"userGroupId\", \"app\")\r\n, CONSTRAINT FK_umbracoUserGroup2App_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2482298Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2610224Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2612373Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2642847Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserGroup2Permission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2Permission UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2Permission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2670832Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2Permission_userGroupKey\" ON \"umbracoUserGroup2Permission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2679716Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2884912Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2886287Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2899251Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserGroup2GranularPermission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"uniqueId\" TEXT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserGroup2GranularPermissionDto UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2924080Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UserGroupKey_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2940853Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2945624Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2945981Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2946171Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2951084Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserStartNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NOT NULL\r\n, \"startNode\" INTEGER NOT NULL\r\n, \"startNodeType\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userStartNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoNode_id FOREIGN KEY (\"startNode\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2954329Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserStartNode_startNodeType\" ON \"umbracoUserStartNode\" (\"startNodeType\",\"startNode\",\"userId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2956477Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2956800Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2957137Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2961093Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE cmsContentNu\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"data\" TEXT COLLATE NOCASE NULL\r\n, \"rv\" INTEGER NOT NULL\r\n, \"dataRaw\" BLOB NULL\r\n, CONSTRAINT PK_cmsContentNu PRIMARY KEY (\"nodeId\", \"published\")\r\n, CONSTRAINT FK_cmsContentNu_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2963182Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_cmsContentNu_published\" ON \"cmsContentNu\" (\"published\",\"nodeId\",\"rv\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2964739Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2964984Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2965222Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2968308Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDocumentVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"templateId\" INTEGER NULL\r\n, \"published\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocumentVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentVersion_cmsTemplate_nodeId FOREIGN KEY (\"templateId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2980127Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_id_published\" ON \"umbracoDocumentVersion\" (\"id\",\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2987726Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_published\" ON \"umbracoDocumentVersion\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2993526Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2994796Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.2995317Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3011993Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDocumentUrl\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL\r\n, \"isDraft\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"urlSegment\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isPrimary\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoDocumentUrl_isPrimary\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoDocumentUrl UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3021486Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentUrl\" ON \"umbracoDocumentUrl\" (\"uniqueId\",\"languageId\",\"isDraft\",\"urlSegment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3028105Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3029189Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3029678Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3039632Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoKeyValue\r\n(\r\n \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NULL\r\n, \"updated\" TEXT NOT NULL CONSTRAINT \"DF_umbracoKeyValue_updated\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoKeyValue PRIMARY KEY (\"key\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3048301Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3149918Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3150554Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3155535Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserLogin\r\n(\r\n \"sessionId\" TEXT NOT NULL\r\n, \"userId\" INTEGER NOT NULL\r\n, \"loggedInUtc\" TEXT NOT NULL\r\n, \"lastValidatedUtc\" TEXT NOT NULL\r\n, \"loggedOutUtc\" TEXT NULL\r\n, \"ipAddress\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoUserLogin PRIMARY KEY (\"sessionId\")\r\n, CONSTRAINT FK_umbracoUserLogin_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3158223Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUserLogin_lastValidatedUtc\" ON \"umbracoUserLogin\" (\"lastValidatedUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3160063Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3160389Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3160529Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3164123Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoConsent\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"current\" INTEGER NOT NULL\r\n, \"source\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoConsent_createDate\" DEFAULT (DATE())\r\n, \"state\" INTEGER NOT NULL\r\n, \"comment\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoConsent UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3166545Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3167169Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3167380Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3170863Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoAudit\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"performingUserId\" INTEGER NOT NULL\r\n, \"performingDetails\" TEXT COLLATE NOCASE NULL\r\n, \"performingIp\" TEXT COLLATE NOCASE NULL\r\n, \"eventDateUtc\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAudit_eventDateUtc\" DEFAULT (DATE())\r\n, \"affectedUserId\" INTEGER NOT NULL\r\n, \"affectedDetails\" TEXT COLLATE NOCASE NULL\r\n, \"eventType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventDetails\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoAudit UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3174237Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3174677Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3174841Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3178532Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoContentVersionCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"availableUserId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoContentVersionCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoUser_id FOREIGN KEY (\"availableUserId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3181172Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoContentVersionCultureVariation_VersionId\" ON \"umbracoContentVersionCultureVariation\" (\"versionId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3182863Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoContentVersionCultureVariation_LanguageId\" ON \"umbracoContentVersionCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3184658Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3184958Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3185193Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3188294Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoDocumentCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, \"available\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDocumentCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3190294Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentCultureVariation_NodeId\" ON \"umbracoDocumentCultureVariation\" (\"nodeId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3191773Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoDocumentCultureVariation_LanguageId\" ON \"umbracoDocumentCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3193083Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3193440Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3193601Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3196378Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoContentSchedule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoContentSchedule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3198223Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3198509Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3198651Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3200884Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoLogViewerQuery\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"query\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLogViewerQuery UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3202511Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_LogViewerQuery_name\" ON \"umbracoLogViewerQuery\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3204005Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3223967Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3224381Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3227422Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoContentVersionCleanupPolicy\r\n(\r\n \"contentTypeId\" INTEGER NOT NULL\r\n, \"preventCleanup\" INTEGER NOT NULL\r\n, \"keepAllVersionsNewerThanDays\" INTEGER NULL\r\n, \"keepLatestVersionPerDayForDays\" INTEGER NULL\r\n, \"updated\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoContentVersionCleanupPolicy PRIMARY KEY (\"contentTypeId\")\r\n, CONSTRAINT FK_umbracoContentVersionCleanupPolicy_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3229765Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3230084Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3230316Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3233048Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoCreatedPackageSchema\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoCreatedPackageSchema_updateDate\" DEFAULT (DATE())\r\n, \"packageId\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoCreatedPackageSchema UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3234925Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoCreatedPackageSchema_Name\" ON \"umbracoCreatedPackageSchema\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3237041Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3237372Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3237602Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3240288Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserGroup2Language\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userGroup2language PRIMARY KEY (\"userGroupId\", \"languageId\")\r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  ON DELETE CASCADE \r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3242099Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3242345Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3242479Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3246026Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhook\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"enabled\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhook UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3247764Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3248050Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3248198Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3250498Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhook2ContentTypeKeys\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"entityKey\" TEXT NOT NULL\r\n, CONSTRAINT PK_webhookEntityKey2Webhook PRIMARY KEY (\"webhookId\", \"entityKey\")\r\n, CONSTRAINT FK_umbracoWebhook2ContentTypeKeys_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3252696Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3252931Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3253065Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3255693Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhook2Events\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"event\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_webhookEvent2WebhookDto PRIMARY KEY (\"webhookId\", \"event\")\r\n, CONSTRAINT FK_umbracoWebhook2Events_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3257430Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3257821Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3258043Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3261010Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhook2Headers\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"Key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_heaeders2WebhookDto PRIMARY KEY (\"webhookId\", \"key\")\r\n, CONSTRAINT FK_umbracoWebhook2Headers_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3263403Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3264012Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3264190Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3268509Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhookLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"key\" TEXT NOT NULL\r\n, \"statusCode\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, \"requestHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"exceptionOccured\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookLog UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3270600Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoWebhookLog_date\" ON \"umbracoWebhookLog\" (\"date\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3272479Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3272758Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3272899Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3275328Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoWebhookRequest\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"eventName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestObject\" TEXT COLLATE NOCASE NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookRequest UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3277068Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3277475Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3277692Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3281046Z","@mt":"Create table:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE TABLE umbracoUserData\r\n(\r\n \"key\" TEXT NOT NULL\r\n, \"userKey\" TEXT NOT NULL\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"identifier\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserDataDto PRIMARY KEY (\"key\")\r\n, CONSTRAINT FK_umbracoUserData_umbracoUser_key FOREIGN KEY (\"userKey\") REFERENCES \"umbracoUser\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3283486Z","@mt":"Create Index:\n {Sql}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","Sql":"CREATE  INDEX \"IX_umbracoUserDataDto_UserKey_Group_Identifier\" ON \"umbracoUserData\" (\"userKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3285208Z","@mt":"Creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3285608Z","@mt":"Completed creating data in {TableName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3285769Z","@mt":"New table {TableName} was created","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:32.3305388Z","@mt":"Database configuration status: {DbConfigStatus}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","DbConfigStatus":"<p>Installation completed!</p>","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.4811316Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.4811826Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.4951130Z","@mt":"Skipping {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.4951570Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.5135083Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.5869397Z","@mt":"No last synced Id found, this generally means this is a new server/install. A cold boot will be triggered.","@l":"Warning","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","SourceContext":"Umbraco.Cms.Infrastructure.Sync.SyncBootStateAccessor","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.9339677Z","@mt":"Telemetry level set to {telemetryLevel} by {username}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","telemetryLevel":"Detailed","username":"<EMAIL>","SourceContext":"Umbraco.Cms.Core.Services.MetricsConsentService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.9435576Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.9436355Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:33.9437899Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.0038790Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.0043260Z","@mt":"Checking if {StepName} requires execution","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.0045858Z","@mt":"Running {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.2597146Z","@mt":"Rebuilding all URLs.","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","SourceContext":"Umbraco.Cms.Core.Services.DocumentUrlService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.3029849Z","@mt":"Database cache was serialized using {CurrentSerializer}. Currently configured cache serializer {Serializer}. Rebuilding database cache.","@l":"Warning","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","CurrentSerializer":"0","Serializer":"MessagePack","SourceContext":"Umbraco.Cms.Infrastructure.HybridCache.DatabaseCacheRebuilder","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.3075784Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StartMessage":"Rebuilding database cache with MessagePack serializer","TimingId":"5dbd8dd","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.5183352Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","EndMessage":"Completed.","Duration":210,"TimingId":"5dbd8dd","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:34.5309150Z","@mt":"Finished {StepName}","@tr":"86bd9da9d5d95c7293e7f61c9ef33549","@sp":"b292311c2395b1e1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2013b843-9c0e-45f7-8352-c0325db19372","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"1cbddc97-ef4d-4de8-a713-d1a510cc80f7","HttpRequestNumber":1,"HttpSessionId":"f7a992fc-cd8b-99f7-fc15-b2c6dfed1212"}
{"@t":"2025-07-14T13:44:35.1941615Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"46896e0470bb0288f7c3a129b0388730","@sp":"c1fb1edc5bec652e","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"3341bdce-ebe3-4e2f-a292-609341ffea7f","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:35.2039332Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"46896e0470bb0288f7c3a129b0388730","@sp":"c1fb1edc5bec652e","Request":"{\r\n  \"redirect_uri\": \"http://localhost:16385/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"JXOjC1vEeB\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"-uW2epzwxA1eWWiWjOk1Z7VhDihe7IoUqi8mqL05j6E\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"3341bdce-ebe3-4e2f-a292-609341ffea7f","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:35.2459618Z","@mt":"The authorization request was successfully validated.","@tr":"46896e0470bb0288f7c3a129b0388730","@sp":"c1fb1edc5bec652e","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"3341bdce-ebe3-4e2f-a292-609341ffea7f","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.2443166Z","@mt":"Revoking active tokens for user with ID {id}","@tr":"f848c1a8175581625522ce6dba466557","@sp":"0e585dd768f2d0af","id":-1,"SourceContext":"Umbraco.Cms.Api.Management.Handlers.RevokeUserAuthenticationTokensNotificationHandler","ActionId":"1872c45d-2cdd-436d-866e-ce2a4120d459","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Login (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/login","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":23,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"3719f550-1fd2-491b-ac9d-efcdd6775629","HttpRequestNumber":3,"HttpSessionId":"ffa00230-826d-3464-dd45-3771f2fc5e47"}
{"@t":"2025-07-14T13:44:59.3737124Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"4a1dcc05b0d415d810522445999abbbc","@sp":"c7d275de1cdb9f49","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000034","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":16,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a78ced30-d316-46c4-900e-aeb2a2f8db92","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.3744355Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"4a1dcc05b0d415d810522445999abbbc","@sp":"c7d275de1cdb9f49","Request":"{\r\n  \"redirect_uri\": \"http://localhost:16385/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"JXOjC1vEeB\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"-uW2epzwxA1eWWiWjOk1Z7VhDihe7IoUqi8mqL05j6E\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000034","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":16,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a78ced30-d316-46c4-900e-aeb2a2f8db92","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.3785404Z","@mt":"The authorization request was successfully validated.","@tr":"4a1dcc05b0d415d810522445999abbbc","@sp":"c7d275de1cdb9f49","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000034","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":16,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a78ced30-d316-46c4-900e-aeb2a2f8db92","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.5623508Z","@mt":"An ad hoc authorization was automatically created and associated with the '{ClientId}' application: {Identifier}.","@tr":"4a1dcc05b0d415d810522445999abbbc","@sp":"c7d275de1cdb9f49","ClientId":"umbraco-back-office","Identifier":"24ef4042-c5b9-453a-a150-be1ae8312bb3","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"44640c1e-08bf-4e59-8e72-eaa53aff2c93","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:00000034","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":16,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a78ced30-d316-46c4-900e-aeb2a2f8db92","HttpRequestNumber":4,"HttpSessionId":"ce001907-b6ee-d26f-11d2-c7e378ff18f3"}
{"@t":"2025-07-14T13:44:59.6935879Z","@mt":"The authorization response was successfully returned to '{RedirectUri}' using the query response mode: {Response}.","@tr":"4a1dcc05b0d415d810522445999abbbc","@sp":"c7d275de1cdb9f49","RedirectUri":"http://localhost:16385/umbraco/oauth_complete","Response":"{\r\n  \"code\": \"[redacted]\",\r\n  \"state\": \"JXOjC1vEeB\",\r\n  \"iss\": \"http://localhost:16385/\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"44640c1e-08bf-4e59-8e72-eaa53aff2c93","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:00000034","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":16,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a78ced30-d316-46c4-900e-aeb2a2f8db92","HttpRequestNumber":4,"HttpSessionId":"ce001907-b6ee-d26f-11d2-c7e378ff18f3"}
{"@t":"2025-07-14T13:44:59.8365458Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"eee334dedbc50da2369c3e77d1ce210b","@sp":"83d2979a9e3fbf84","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000039","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c48d9f1c-8324-4704-ba2a-f3232a545f71","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.8429311Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"eee334dedbc50da2369c3e77d1ce210b","@sp":"83d2979a9e3fbf84","Request":"{\r\n  \"grant_type\": \"authorization_code\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:16385/umbraco/oauth_complete\",\r\n  \"code\": \"[redacted]\",\r\n  \"code_verifier\": \"MVkuxdbA3e5WfHjAbvNa8hFn1cJPykWrUpxxJQyjGFN3IOmPq9WBwpyD7ZyCh9gmwfMeX2tw6BB3W3F8w5G18MspIR9zdX7KRRexYnq4BJvbMCMfrLzSARfDg6IUJFiz\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000039","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c48d9f1c-8324-4704-ba2a-f3232a545f71","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.9436306Z","@mt":"The token request was successfully validated.","@tr":"eee334dedbc50da2369c3e77d1ce210b","@sp":"83d2979a9e3fbf84","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:00000039","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c48d9f1c-8324-4704-ba2a-f3232a545f71","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:44:59.9672121Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"eee334dedbc50da2369c3e77d1ce210b","@sp":"83d2979a9e3fbf84","Identifier":"e907f0c3-1fcc-447d-8d56-1cfa79b1876b","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:00000039","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c48d9f1c-8324-4704-ba2a-f3232a545f71","HttpRequestNumber":5,"HttpSessionId":"668b9059-29fb-0053-6721-3f976d49556b"}
{"@t":"2025-07-14T13:44:59.9987951Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"eee334dedbc50da2369c3e77d1ce210b","@sp":"83d2979a9e3fbf84","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 299,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:00000039","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c48d9f1c-8324-4704-ba2a-f3232a545f71","HttpRequestNumber":5,"HttpSessionId":"668b9059-29fb-0053-6721-3f976d49556b"}
{"@t":"2025-07-14T13:45:00.0177068Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"b5ce7253d11cf0d860f1a106143156e6","@sp":"d3d57747662e8d08","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000003A","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"99c18f31-7ea8-4fb8-897e-8dc8addbb509","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:45:00.0184202Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"b5ce7253d11cf0d860f1a106143156e6","@sp":"d3d57747662e8d08","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:16385/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000003A","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"99c18f31-7ea8-4fb8-897e-8dc8addbb509","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:45:00.0381843Z","@mt":"The token request was successfully validated.","@tr":"b5ce7253d11cf0d860f1a106143156e6","@sp":"d3d57747662e8d08","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAH6:0000003A","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"99c18f31-7ea8-4fb8-897e-8dc8addbb509","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:45:00.0500130Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"b5ce7253d11cf0d860f1a106143156e6","@sp":"d3d57747662e8d08","Identifier":"0bba729f-4872-43c7-993a-f7124f4851c9","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000003A","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"99c18f31-7ea8-4fb8-897e-8dc8addbb509","HttpRequestNumber":6,"HttpSessionId":"9878990b-011f-3bb1-439d-e33ef038745a"}
{"@t":"2025-07-14T13:45:00.0687002Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"b5ce7253d11cf0d860f1a106143156e6","@sp":"d3d57747662e8d08","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAH6:0000003A","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAH6","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":25,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"99c18f31-7ea8-4fb8-897e-8dc8addbb509","HttpRequestNumber":6,"HttpSessionId":"9878990b-011f-3bb1-439d-e33ef038745a"}
{"@t":"2025-07-14T13:45:35.2917501Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":20,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-14T13:48:09.1021381Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"387c99cc364fc405cd497846779e6b92","@sp":"950594fe14578b18","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000205","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"958f0690-dfe8-422e-9c36-0b3f0810425c","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.1038470Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"387c99cc364fc405cd497846779e6b92","@sp":"950594fe14578b18","Request":"{\r\n  \"redirect_uri\": \"https://localhost:44301/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"EKF1gJll5u\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"0EH2BW8WLHFKm8YZPm6nOqmvqTvc4Ku9j4TvtvGCaEA\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000205","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"958f0690-dfe8-422e-9c36-0b3f0810425c","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.1169450Z","@mt":"The authorization request was successfully validated.","@tr":"387c99cc364fc405cd497846779e6b92","@sp":"950594fe14578b18","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000205","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"958f0690-dfe8-422e-9c36-0b3f0810425c","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.1309653Z","@mt":"An ad hoc authorization was automatically created and associated with the '{ClientId}' application: {Identifier}.","@tr":"387c99cc364fc405cd497846779e6b92","@sp":"950594fe14578b18","ClientId":"umbraco-back-office","Identifier":"2acd38d0-52a1-4c75-8e7e-2315e0e20009","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"44640c1e-08bf-4e59-8e72-eaa53aff2c93","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000205","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"958f0690-dfe8-422e-9c36-0b3f0810425c","HttpRequestNumber":7,"HttpSessionId":"4047c366-5918-8388-ae52-71993a5edbe2"}
{"@t":"2025-07-14T13:48:09.1384822Z","@mt":"The authorization response was successfully returned to '{RedirectUri}' using the query response mode: {Response}.","@tr":"387c99cc364fc405cd497846779e6b92","@sp":"950594fe14578b18","RedirectUri":"https://localhost:44301/umbraco/oauth_complete","Response":"{\r\n  \"code\": \"[redacted]\",\r\n  \"state\": \"EKF1gJll5u\",\r\n  \"iss\": \"https://localhost:44301/\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"44640c1e-08bf-4e59-8e72-eaa53aff2c93","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000205","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"958f0690-dfe8-422e-9c36-0b3f0810425c","HttpRequestNumber":7,"HttpSessionId":"4047c366-5918-8388-ae52-71993a5edbe2"}
{"@t":"2025-07-14T13:48:09.2251131Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"d3b066ec973d63705122a162ccdaabb9","@sp":"c4bb26bb1543b0c8","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000213","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":42,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"31a4736b-a2d0-40f3-a2d1-e86ea5e7cad5","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2258910Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"d3b066ec973d63705122a162ccdaabb9","@sp":"c4bb26bb1543b0c8","Request":"{\r\n  \"grant_type\": \"authorization_code\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44301/umbraco/oauth_complete\",\r\n  \"code\": \"[redacted]\",\r\n  \"code_verifier\": \"KzlCuEuM8vb8YGgBtmTrExknrj1otAPb2IEunclWlkC0wx5M8WPiM4NyAZBI0jRx9di2zRXeuOuNZlAH9MPJ564rSNsLVEodBwX40L28MSt1ax12VmTV7Yx36xR8Mlyr\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000213","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":42,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"31a4736b-a2d0-40f3-a2d1-e86ea5e7cad5","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2375172Z","@mt":"The token request was successfully validated.","@tr":"d3b066ec973d63705122a162ccdaabb9","@sp":"c4bb26bb1543b0c8","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000213","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":42,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"31a4736b-a2d0-40f3-a2d1-e86ea5e7cad5","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2421472Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"d3b066ec973d63705122a162ccdaabb9","@sp":"c4bb26bb1543b0c8","Identifier":"041e2f97-69c5-485b-98c9-83c18a7978f5","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000213","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":42,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"31a4736b-a2d0-40f3-a2d1-e86ea5e7cad5","HttpRequestNumber":8,"HttpSessionId":"f32360d3-b158-8bc8-e384-ce4fcadf66c5"}
{"@t":"2025-07-14T13:48:09.2531106Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"d3b066ec973d63705122a162ccdaabb9","@sp":"c4bb26bb1543b0c8","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000213","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":42,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"31a4736b-a2d0-40f3-a2d1-e86ea5e7cad5","HttpRequestNumber":8,"HttpSessionId":"f32360d3-b158-8bc8-e384-ce4fcadf66c5"}
{"@t":"2025-07-14T13:48:09.2604451Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"b7e2beba5053936088534be982ec8f10","@sp":"328dbeada372c7c1","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000215","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"de06160b-b356-4fe4-b3e1-e1110fdee02b","HttpRequestNumber":9,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2605785Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"b7e2beba5053936088534be982ec8f10","@sp":"328dbeada372c7c1","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44301/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000215","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"de06160b-b356-4fe4-b3e1-e1110fdee02b","HttpRequestNumber":9,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2629806Z","@mt":"The token request was successfully validated.","@tr":"b7e2beba5053936088534be982ec8f10","@sp":"328dbeada372c7c1","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE2RHOMNAHM:00000215","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"de06160b-b356-4fe4-b3e1-e1110fdee02b","HttpRequestNumber":9,"HttpSessionId":"0"}
{"@t":"2025-07-14T13:48:09.2666092Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"b7e2beba5053936088534be982ec8f10","@sp":"328dbeada372c7c1","Identifier":"b982d306-209d-4672-9224-b35f625312bf","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000215","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"de06160b-b356-4fe4-b3e1-e1110fdee02b","HttpRequestNumber":9,"HttpSessionId":"d237272a-0ca3-37d1-1945-66f532508e10"}
{"@t":"2025-07-14T13:48:09.2733473Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"b7e2beba5053936088534be982ec8f10","@sp":"328dbeada372c7c1","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"7ac8f556-96e3-4a70-8926-b0faebc3978f","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE2RHOMNAHM:00000215","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE2RHOMNAHM","ProcessId":31476,"ProcessName":"MddPlus","ThreadId":15,"ApplicationId":"48181f4bb424fae1de6580fa17fc1e773ea239f5","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"de06160b-b356-4fe4-b3e1-e1110fdee02b","HttpRequestNumber":9,"HttpSessionId":"d237272a-0ca3-37d1-1945-66f532508e10"}
