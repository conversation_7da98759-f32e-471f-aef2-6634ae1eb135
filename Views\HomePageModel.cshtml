@using Umbraco.Cms.Web.Common.PublishedModels;
@using MddPlus.Helpers;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<HomePageModel>
@{
    Layout = "Master.cshtml";
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
    var isRtl = TranslationHelper.IsRTL(Context);
    var isArabic = currentLanguage == "ar";

    // SEO Meta Tags
    ViewBag.Title = isArabic ? Model.TitleAr : Model.TitleEn;
    ViewBag.Description = isArabic ? Model.DescriptionAr : Model.DescriptionEn;
    ViewBag.Keywords = isArabic ? "تمويل جماعي، شريعة إسلامية، استثمار، مدد بلس" : "crowdfunding, sharia compliant, investment, MDD Plus";
}

@section Head {
    <meta property="og:title" content="@ViewBag.Title">
    <meta property="og:description" content="@ViewBag.Description">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="@(isArabic ? "ar_SA" : "en_US")">
}

<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden section-padding" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative container mx-auto">
        <div class="text-center text-white max-w-4xl mx-auto stagger-animation">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
                <span class="text-sm font-medium">🇸🇦 @(isArabic ? "دعم رؤية المملكة 2030" : "Supporting Saudi Vision 2030")</span>
            </div>

            <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight @(isArabic ? "font-arabic" : "font-english")">
                @Model.GetHeroTitle(currentLanguage)
            </h1>

            <p class="text-xl lg:text-2xl text-gray-200 leading-relaxed mb-8">
                @Model.GetHeroSubtitle(currentLanguage)
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <a href="@Model.CtaButton1Link" class="btn-primary btn-advanced text-lg px-8 py-4 hover-lift">
                    💰 @Model.GetCtaButton1Text(currentLanguage)
                </a>
                <a href="@Model.CtaButton2Link" class="btn-secondary text-lg px-8 py-4 hover-lift">
                    🏦 @Model.GetCtaButton2Text(currentLanguage)
                </a>
            </div>

            <div class="flex flex-wrap justify-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>@(isArabic ? "مرخصة من هيئة السوق المالية" : "Licensed by CMA")</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>@(isArabic ? "متوافقة مع الشريعة الإسلامية" : "Sharia Compliant")</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>@(isArabic ? "آمنة ومضمونة 100%" : "100% Secure & Guaranteed")</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="section-padding bg-white dark:bg-gray-900" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                @(isArabic ? "أرقام تتحدث عن نفسها" : "Numbers That Speak for Themselves")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? "نتائج متميزة وعوائد مجزية لمستثمرينا" : "Outstanding results and attractive returns for our investors")
            </p>
        </div>

        <div class="grid-responsive">
            @foreach (var statistic in Model.Statistics.OrderBy(s => s.SortOrder))
            {
                <div class="card-advanced text-center hover-glow animate-on-scroll" style="animation-delay: @(statistic.AnimationDelay)ms">
                    <div class="text-4xl lg:text-5xl font-bold @statistic.GetColorClass() mb-2 stat-number" data-target="@statistic.Number">
                        @statistic.Icon @<EMAIL>
                    </div>
                    <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">
                        @statistic.GetLabel(currentLanguage)
                    </div>
                    <div class="text-gray-500 dark:text-gray-400">
                        @statistic.GetDescription(currentLanguage)
                    </div>
                    @if (statistic.ProgressPercentage > 0)
                    {
                        <div class="progress-advanced mt-4">
                            <div class="progress-bar" style="width: @(statistic.ProgressPercentage)%"></div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="section-padding bg-gray-50 dark:bg-gray-800" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                @(isArabic ? "لماذا تختار مدد بلس؟" : "Why Choose MDD Plus?")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? "نقدم منصة آمنة وموثوقة للتمويل الجماعي المتوافق مع الشريعة الإسلامية" : "We provide a secure and trusted platform for Sharia-compliant crowdfunding")
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach (var feature in Model.Features.OrderBy(f => f.SortOrder))
            {
                <div class="card-advanced text-@(isRtl ? "right" : "left") feature-item hover-lift @(feature.IsHighlighted ? "border-2 border-blue-500" : "")">
                    <div class="w-16 h-16 bg-gradient-to-br @feature.GetGradientClass() rounded-2xl flex items-center justify-center mb-6 feature-icon">
                        <span class="text-2xl">@feature.Icon</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                        @feature.GetTitle(currentLanguage)
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                        @feature.GetDescription(currentLanguage)
                    </p>

                    @if (feature.IsHighlighted)
                    {
                        <div class="inline-flex items-center text-blue-600 dark:text-blue-400 font-medium">
                            <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            @(isArabic ? "ميزة مميزة" : "Featured")
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(feature.LinkUrl))
                    {
                        <div class="mt-4">
                            <a href="@feature.LinkUrl" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
                                @feature.GetLinkText(currentLanguage) →
                            </a>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding hero-gradient relative overflow-hidden" dir="@(isRtl ? "rtl" : "ltr")">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative container mx-auto text-center">
        <div class="max-w-4xl mx-auto text-white">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                @Model.GetCtaTitle(currentLanguage)
            </h2>
            <p class="text-xl mb-8 text-gray-200">
                @Model.GetCtaSubtitle(currentLanguage)
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="@Model.CtaButton1Link" class="btn-primary btn-advanced text-lg px-10 py-4 bg-white text-mdd-primary hover:bg-gray-100 shadow-2xl hover-lift">
                    💰 @Model.GetCtaButton1Text(currentLanguage)
                </a>
                <a href="@Model.CtaButton2Link" class="btn-secondary text-lg px-10 py-4 border-2 border-white text-white hover:bg-white hover:text-mdd-primary hover-lift">
                    📖 @Model.GetCtaButton2Text(currentLanguage)
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Floating Action Button -->
<button class="btn-floating no-print" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" data-tooltip="@(isArabic ? "العودة للأعلى" : "Back to top")">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

@section Scripts {
    <script>
        // Dynamic content specific scripts
        document.addEventListener('DOMContentLoaded', function() {
            // Animate statistics counters
            const statNumbers = document.querySelectorAll('.stat-number[data-target]');
            const animateCounter = (element) => {
                const target = parseInt(element.dataset.target);
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = element.textContent.replace(/[\d,]+/, Math.floor(current).toLocaleString());
                }, 20);
            };

            // Intersection Observer for counter animation
            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounter(entry.target);
                        counterObserver.unobserve(entry.target);
                    }
                });
            });

            statNumbers.forEach(stat => counterObserver.observe(stat));

            // Enhanced feature cards animation
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('animate-slide-bottom');
            });

            // Dynamic content loaded notification
            if (window.advancedFeatures) {
                window.advancedFeatures.showNotification(
                    '@(isArabic ? "تم تحميل المحتوى الديناميكي بنجاح!" : "Dynamic content loaded successfully!")',
                    'success',
                    3000
                );
            }
        });
    </script>
}
