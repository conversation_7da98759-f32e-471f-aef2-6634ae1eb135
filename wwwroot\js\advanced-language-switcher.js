// Advanced Language Switcher with Full Translation Support for MDD Plus

class AdvancedLanguageManager {
    constructor() {
        this.currentLanguage = this.getCurrentLanguage();
        this.supportedLanguages = ['ar', 'en'];
        this.languageData = {
            ar: {
                name: 'العربية',
                direction: 'rtl',
                fontFamily: 'Cairo, sans-serif'
            },
            en: {
                name: 'English',
                direction: 'ltr',
                fontFamily: 'Inter, sans-serif'
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupLanguageToggle();
        this.updatePageLanguage();
        this.setupKeyboardNavigation();
        this.detectLanguageFromURL();
        this.setupLanguageDetection();
    }
    
    getCurrentLanguage() {
        // Check URL path first
        const path = window.location.pathname;
        if (path.startsWith('/en')) return 'en';
        if (path.startsWith('/ar')) return 'ar';
        
        // Check localStorage
        const savedLanguage = localStorage.getItem('mdd-language');
        if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
            return savedLanguage;
        }
        
        // Check cookie
        const cookieLanguage = this.getCookie('language');
        if (cookieLanguage && this.supportedLanguages.includes(cookieLanguage)) {
            return cookieLanguage;
        }
        
        // Check browser language
        const browserLanguage = navigator.language || navigator.userLanguage;
        if (browserLanguage.startsWith('ar')) return 'ar';
        if (browserLanguage.startsWith('en')) return 'en';
        
        // Default to Arabic
        return 'ar';
    }
    
    setupLanguageToggle() {
        const languageToggle = document.getElementById('language-toggle');
        const mobileLanguageToggle = document.getElementById('mobile-language-toggle');
        
        if (languageToggle) {
            languageToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLanguageMenu(languageToggle);
            });
        }
        
        if (mobileLanguageToggle) {
            mobileLanguageToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleLanguage();
            });
        }
        
        // Update toggle text
        this.updateLanguageToggleText();
    }
    
    showLanguageMenu(toggleElement) {
        // Remove existing menu
        const existingMenu = document.querySelector('.language-menu');
        if (existingMenu) {
            existingMenu.remove();
            return;
        }
        
        // Create language menu
        const menu = document.createElement('div');
        menu.className = 'language-menu absolute top-full right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-[150px]';
        
        this.supportedLanguages.forEach(lang => {
            const item = document.createElement('button');
            item.className = `w-full text-right px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center justify-between ${lang === this.currentLanguage ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`;
            
            const flag = lang === 'ar' ? '🇸🇦' : '🇺🇸';
            const name = this.languageData[lang].name;
            
            item.innerHTML = `
                <span class="flex items-center gap-2">
                    <span class="text-lg">${flag}</span>
                    <span>${name}</span>
                </span>
                ${lang === this.currentLanguage ? '<span class="text-blue-500">✓</span>' : ''}
            `;
            
            item.addEventListener('click', () => {
                this.switchToLanguage(lang);
                menu.remove();
            });
            
            menu.appendChild(item);
        });
        
        // Position menu
        toggleElement.parentElement.style.position = 'relative';
        toggleElement.parentElement.appendChild(menu);
        
        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!menu.contains(e.target) && !toggleElement.contains(e.target)) {
                    menu.remove();
                }
            }, { once: true });
        }, 100);
    }
    
    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.switchToLanguage(newLanguage);
    }
    
    switchToLanguage(language) {
        if (!this.supportedLanguages.includes(language)) {
            console.warn(`Language ${language} is not supported`);
            return;
        }
        
        // Save language preference
        localStorage.setItem('mdd-language', language);
        this.setCookie('language', language, 365);
        
        // Update current language
        this.currentLanguage = language;
        
        // Get new URL
        const newUrl = this.getLanguageUrl(language);
        
        // Show loading indicator
        this.showLoadingIndicator();
        
        // Navigate to new URL
        window.location.href = newUrl;
    }
    
    getLanguageUrl(language) {
        let currentPath = window.location.pathname;
        const currentSearch = window.location.search;
        const currentHash = window.location.hash;
        
        // Remove current language prefix
        if (currentPath.startsWith('/ar/')) {
            currentPath = currentPath.substring(3);
        } else if (currentPath.startsWith('/en/')) {
            currentPath = currentPath.substring(3);
        }
        
        // Ensure path starts with /
        if (!currentPath.startsWith('/')) {
            currentPath = '/' + currentPath;
        }
        
        // Add new language prefix (Arabic is default, no prefix needed)
        let newPath = currentPath;
        if (language === 'en') {
            newPath = '/en' + currentPath;
        }
        
        return newPath + currentSearch + currentHash;
    }
    
    updatePageLanguage() {
        const html = document.documentElement;
        const body = document.body;
        const langData = this.languageData[this.currentLanguage];
        
        // Update HTML attributes
        html.setAttribute('lang', this.currentLanguage);
        html.setAttribute('dir', langData.direction);
        
        // Update body classes
        body.className = body.className.replace(/font-(arabic|english)/g, '');
        body.classList.add(`font-${this.currentLanguage === 'ar' ? 'arabic' : 'english'}`);
        
        // Update CSS custom properties
        document.documentElement.style.setProperty('--font-family', langData.fontFamily);
        document.documentElement.style.setProperty('--text-direction', langData.direction);
        
        // Update meta tags
        this.updateMetaTags();
    }
    
    updateMetaTags() {
        // Update language meta tag
        let langMeta = document.querySelector('meta[name=\"language\"]');
        if (!langMeta) {
            langMeta = document.createElement('meta');
            langMeta.name = 'language';
            document.head.appendChild(langMeta);
        }
        langMeta.content = this.currentLanguage;
        
        // Update direction meta tag
        let dirMeta = document.querySelector('meta[name=\"direction\"]');
        if (!dirMeta) {
            dirMeta = document.createElement('meta');
            dirMeta.name = 'direction';
            document.head.appendChild(dirMeta);
        }
        dirMeta.content = this.languageData[this.currentLanguage].direction;
    }
    
    updateLanguageToggleText() {
        const languageToggle = document.getElementById('language-toggle');
        const mobileLanguageToggle = document.getElementById('mobile-language-toggle');
        
        const oppositeLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        const oppositeLanguageName = this.languageData[oppositeLanguage].name;
        const oppositeFlag = oppositeLanguage === 'ar' ? '🇸🇦' : '🇺🇸';
        
        if (languageToggle) {
            const span = languageToggle.querySelector('span');
            if (span) {
                span.innerHTML = `${oppositeFlag} ${oppositeLanguageName}`;
            }
        }
        
        if (mobileLanguageToggle) {
            const span = mobileLanguageToggle.querySelector('span');
            if (span) {
                span.innerHTML = `${oppositeFlag} ${oppositeLanguageName}`;
            }
        }
    }
    
    detectLanguageFromURL() {
        const path = window.location.pathname;
        let detectedLanguage = 'ar'; // default
        
        if (path.startsWith('/en')) {
            detectedLanguage = 'en';
        } else if (path.startsWith('/ar')) {
            detectedLanguage = 'ar';
        }
        
        if (detectedLanguage !== this.currentLanguage) {
            this.currentLanguage = detectedLanguage;
            this.updatePageLanguage();
        }
    }
    
    setupLanguageDetection() {
        // Detect language changes from browser back/forward
        window.addEventListener('popstate', () => {
            this.detectLanguageFromURL();
            this.updateLanguageToggleText();
        });
        
        // Detect language from URL hash changes
        window.addEventListener('hashchange', () => {
            this.detectLanguageFromURL();
        });
    }
    
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Alt + L to toggle language
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                this.toggleLanguage();
            }
            
            // Alt + M to show language menu
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                const languageToggle = document.getElementById('language-toggle');
                if (languageToggle) {
                    this.showLanguageMenu(languageToggle);
                }
            }
        });
    }
    
    showLoadingIndicator() {
        // Create loading overlay
        const overlay = document.createElement('div');
        overlay.id = 'language-loading';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3 shadow-xl">
                <div class="loading-spinner"></div>
                <span class="text-gray-700 dark:text-gray-300">${this.currentLanguage === 'ar' ? 'جاري تغيير اللغة...' : 'Changing language...'}</span>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }
    
    // Utility methods
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }
    
    setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
    }
    
    // Public API methods
    getLanguageInfo() {
        return {
            current: this.currentLanguage,
            data: this.languageData[this.currentLanguage],
            supported: this.supportedLanguages
        };
    }
    
    isRTL() {
        return this.languageData[this.currentLanguage].direction === 'rtl';
    }
    
    getOppositeLanguage() {
        return this.currentLanguage === 'ar' ? 'en' : 'ar';
    }
    
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Use Arabic-Indic numerals
            return number.toString().replace(/\\d/g, (digit) => {
                const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
                return arabicNumerals[parseInt(digit)];
            });
        }
        return number.toLocaleString('en-US');
    }
    
    formatCurrency(amount, currency = 'SAR') {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    formatDate(date, options = {}) {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(new Date(date));
    }
    
    announceLanguageChange() {
        const announcement = this.currentLanguage === 'ar' ? 
            'تم تغيير اللغة إلى العربية' : 
            'Language changed to English';
        
        // Create announcement for screen readers
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;
        
        document.body.appendChild(announcer);
        
        setTimeout(() => {
            if (announcer.parentElement) {
                announcer.remove();
            }
        }, 1000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.advancedLanguageManager = new AdvancedLanguageManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedLanguageManager;
}
