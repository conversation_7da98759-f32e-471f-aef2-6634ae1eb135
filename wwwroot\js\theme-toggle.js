// Theme Toggle Functionality for MDD Plus

class ThemeManager {
    constructor() {
        this.themeToggleBtn = document.getElementById('theme-toggle');
        this.darkIcon = document.getElementById('theme-toggle-dark-icon');
        this.lightIcon = document.getElementById('theme-toggle-light-icon');
        
        this.init();
    }
    
    init() {
        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
            this.enableDarkMode();
        } else {
            this.enableLightMode();
        }
        
        // Add event listener for theme toggle
        if (this.themeToggleBtn) {
            this.themeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                if (e.matches) {
                    this.enableDarkMode();
                } else {
                    this.enableLightMode();
                }
            }
        });
        
        // Update icons on page load
        this.updateIcons();
    }
    
    toggleTheme() {
        if (document.documentElement.classList.contains('dark')) {
            this.enableLightMode();
            localStorage.setItem('theme', 'light');
        } else {
            this.enableDarkMode();
            localStorage.setItem('theme', 'dark');
        }
        
        this.updateIcons();
        this.announceThemeChange();
    }
    
    enableDarkMode() {
        document.documentElement.classList.add('dark');
        this.updateMetaThemeColor('#0F172A');
    }
    
    enableLightMode() {
        document.documentElement.classList.remove('dark');
        this.updateMetaThemeColor('#FFFFFF');
    }
    
    updateIcons() {
        if (!this.darkIcon || !this.lightIcon) return;
        
        if (document.documentElement.classList.contains('dark')) {
            this.darkIcon.classList.add('hidden');
            this.lightIcon.classList.remove('hidden');
        } else {
            this.darkIcon.classList.remove('hidden');
            this.lightIcon.classList.add('hidden');
        }
    }
    
    updateMetaThemeColor(color) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = color;
    }
    
    announceThemeChange() {
        const isDark = document.documentElement.classList.contains('dark');
        const announcement = isDark ? 'Dark mode enabled' : 'Light mode enabled';
        
        // Create a temporary element for screen readers
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;
        
        document.body.appendChild(announcer);
        
        setTimeout(() => {
            document.body.removeChild(announcer);
        }, 1000);
    }
    
    getCurrentTheme() {
        return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    }
    
    // Method to programmatically set theme
    setTheme(theme) {
        if (theme === 'dark') {
            this.enableDarkMode();
            localStorage.setItem('theme', 'dark');
        } else {
            this.enableLightMode();
            localStorage.setItem('theme', 'light');
        }
        this.updateIcons();
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
