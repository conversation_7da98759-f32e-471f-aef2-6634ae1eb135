using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;

namespace MddPlus.Controllers
{
    public class HomeController : UmbracoPageController
    {
        public HomeController(ILogger<UmbracoPageController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
        }

        public IActionResult Index()
        {
            return CurrentTemplate(CurrentPage);
        }
    }
}
