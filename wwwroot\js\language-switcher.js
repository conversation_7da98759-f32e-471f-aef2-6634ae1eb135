// Advanced Language Switcher Functionality for MDD Plus

class LanguageManager {
    constructor() {
        this.languageToggle = document.getElementById('language-toggle');
        this.currentLanguage = this.getCurrentLanguage();
        this.supportedLanguages = ['ar', 'en'];
        this.translations = {};

        this.init();
    }

    init() {
        // Add event listener for language toggle
        if (this.languageToggle) {
            this.languageToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleLanguage();
            });
        }

        // Update language toggle text
        this.updateLanguageToggleText();

        // Set up keyboard navigation
        this.setupKeyboardNavigation();
    }

    getCurrentLanguage() {
        // Get language from HTML lang attribute
        const htmlLang = document.documentElement.lang;
        return htmlLang.startsWith('ar') ? 'ar' : 'en';
    }

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.switchToLanguage(newLanguage);
    }

    switchToLanguage(language) {
        // Store language preference
        localStorage.setItem('preferred-language', language);

        // Get current URL
        const currentUrl = window.location.href;
        const url = new URL(currentUrl);

        // Determine the new URL based on language
        let newUrl;

        if (language === 'ar') {
            // Switch to Arabic
            if (url.pathname.startsWith('/en/')) {
                // Replace /en/ with /ar/
                newUrl = currentUrl.replace('/en/', '/ar/');
            } else if (url.pathname === '/en' || url.pathname === '/en/') {
                // Replace /en with /ar
                newUrl = currentUrl.replace('/en', '/ar');
            } else {
                // Add /ar prefix
                newUrl = url.origin + '/ar' + url.pathname + url.search + url.hash;
            }
        } else {
            // Switch to English
            if (url.pathname.startsWith('/ar/')) {
                // Replace /ar/ with /en/
                newUrl = currentUrl.replace('/ar/', '/en/');
            } else if (url.pathname === '/ar' || url.pathname === '/ar/') {
                // Replace /ar with /en
                newUrl = currentUrl.replace('/ar', '/en');
            } else {
                // Add /en prefix
                newUrl = url.origin + '/en' + url.pathname + url.search + url.hash;
            }
        }

        // Navigate to new URL
        window.location.href = newUrl;
    }

    updateLanguageToggleText() {
        if (!this.languageToggle) return;

        const toggleText = this.languageToggle.querySelector('span');
        if (toggleText) {
            toggleText.textContent = this.currentLanguage === 'ar' ? 'EN' : 'العربية';
        }
    }

    setupKeyboardNavigation() {
        if (!this.languageToggle) return;

        this.languageToggle.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleLanguage();
            }
        });
    }

    // Method to get available languages
    getAvailableLanguages() {
        return [
            { code: 'en', name: 'English', nativeName: 'English' },
            { code: 'ar', name: 'Arabic', nativeName: 'العربية' }
        ];
    }

    // Method to get current language info
    getCurrentLanguageInfo() {
        const languages = this.getAvailableLanguages();
        return languages.find(lang => lang.code === this.currentLanguage);
    }

    // Method to check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }

    // Method to get opposite language
    getOppositeLanguage() {
        return this.currentLanguage === 'ar' ? 'en' : 'ar';
    }

    // Method to format numbers based on language
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Use Arabic-Indic numerals
            return number.toString().replace(/\d/g, (digit) => {
                const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
                return arabicNumerals[parseInt(digit)];
            });
        }
        return number.toString();
    }

    // Method to format currency based on language
    formatCurrency(amount, currency = 'SAR') {
        const formatter = new Intl.NumberFormat(
            this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US',
            {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }
        );

        return formatter.format(amount);
    }

    // Method to format date based on language
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        const formatOptions = { ...defaultOptions, ...options };

        const formatter = new Intl.DateTimeFormat(
            this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US',
            formatOptions
        );

        return formatter.format(new Date(date));
    }

    // Method to announce language change for screen readers
    announceLanguageChange() {
        const newLanguageInfo = this.getCurrentLanguageInfo();
        const announcement = `Language changed to ${newLanguageInfo.nativeName}`;

        // Create a temporary element for screen readers
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;

        document.body.appendChild(announcer);

        setTimeout(() => {
            document.body.removeChild(announcer);
        }, 1000);
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageManager;
}
