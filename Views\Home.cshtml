@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = "_Layout.cshtml";
    ViewBag.Title = "MDD Plus - Sharia-Compliant Debt Crowdfunding Platform";
    ViewBag.Description = "Licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding. Connecting investors and borrowers through trusted financial solutions.";
    
    var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
    var isRtl = currentCulture.TextInfo.IsRightToLeft;
    var isArabic = currentCulture.TwoLetterISOLanguageName == "ar";
}

@section Head {
    <meta property="og:title" content="@ViewBag.Title">
    <meta property="og:description" content="@ViewBag.Description">
    <meta property="og:type" content="website">
    <meta property="og:url" content="@Request.GetDisplayUrl()">
    <meta property="og:image" content="/images/mdd-plus-og-image.jpg">
    <meta name="twitter:card" content="summary_large_image">
}

<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative container mx-auto px-4 py-20 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="@(isRtl ? "text-right lg:text-right" : "text-left lg:text-left") text-white animate-on-scroll">
                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                    @if (isArabic)
                    {
                        <span class="block">منصة التمويل الجماعي</span>
                        <span class="block text-mdd-dark-accent">المتوافقة مع الشريعة</span>
                    }
                    else
                    {
                        <span class="block">Sharia-Compliant</span>
                        <span class="block text-mdd-dark-accent">Crowdfunding Platform</span>
                    }
                </h1>
                
                <p class="text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                    @(isArabic ? 
                        "نربط المستثمرين والمقترضين من خلال حلول مالية موثوقة ومتوافقة مع الشريعة الإسلامية" : 
                        "Connecting investors and borrowers through trusted, Sharia-compliant financial solutions")
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 mb-8">
                    <a href="/register" class="btn-primary text-lg px-8 py-4">
                        @(isArabic ? "ابدأ الاستثمار" : "Start Investing")
                    </a>
                    <a href="/borrowers" class="btn-secondary text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-mdd-primary">
                        @(isArabic ? "احصل على تمويل" : "Get Funding")
                    </a>
                </div>
                
                <!-- Trust Indicators -->
                <div class="flex flex-wrap items-center gap-6 text-sm">
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>@(isArabic ? "مرخصة من هيئة السوق المالية" : "CMA Licensed")</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>@(isArabic ? "متوافقة مع الشريعة" : "Sharia Compliant")</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>@(isArabic ? "آمنة ومضمونة" : "Secure & Guaranteed")</span>
                    </div>
                </div>
            </div>
            
            <!-- Hero Image/Animation -->
            <div class="@(isRtl ? "lg:order-first" : "") animate-on-scroll">
                <div class="relative">
                    <img src="/images/hero-illustration.svg" alt="@(isArabic ? "منصة التمويل الجماعي" : "Crowdfunding Platform")" class="w-full h-auto max-w-lg mx-auto">
                    
                    <!-- Floating Cards Animation -->
                    <div class="absolute top-10 @(isRtl ? "left-10" : "right-10") bg-white/10 backdrop-blur-sm rounded-lg p-4 animate-bounce">
                        <div class="text-white text-center">
                            <div class="text-2xl font-bold">15%</div>
                            <div class="text-sm">@(isArabic ? "عائد سنوي" : "Annual Return")</div>
                        </div>
                    </div>
                    
                    <div class="absolute bottom-10 @(isRtl ? "right-10" : "left-10") bg-white/10 backdrop-blur-sm rounded-lg p-4 animate-pulse">
                        <div class="text-white text-center">
                            <div class="text-2xl font-bold">500M+</div>
                            <div class="text-sm">@(isArabic ? "ريال سعودي" : "SAR Funded")</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-16 bg-white dark:bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center animate-on-scroll">
                <div class="stat-number mb-2" data-target="500">0</div>
                <div class="text-mdd-accent dark:text-gray-400">@(isArabic ? "مليون ريال" : "Million SAR")</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">@(isArabic ? "إجمالي التمويل" : "Total Funded")</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="stat-number mb-2" data-target="1200">0</div>
                <div class="text-mdd-accent dark:text-gray-400">@(isArabic ? "مستثمر" : "Investors")</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">@(isArabic ? "مستثمر نشط" : "Active Investors")</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="stat-number mb-2" data-target="95">0</div>
                <div class="text-mdd-accent dark:text-gray-400">%</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">@(isArabic ? "معدل النجاح" : "Success Rate")</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="stat-number mb-2" data-target="15">0</div>
                <div class="text-mdd-accent dark:text-gray-400">%</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">@(isArabic ? "عائد متوسط" : "Average Return")</div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50 dark:bg-gray-800">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                @(isArabic ? "لماذا تختار مدد بلس؟" : "Why Choose MDD Plus?")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? 
                    "نقدم منصة آمنة وموثوقة للتمويل الجماعي المتوافق مع الشريعة الإسلامية" : 
                    "We provide a secure and trusted platform for Sharia-compliant crowdfunding")
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "آمنة ومرخصة" : "Secure & Licensed")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "مرخصة من هيئة السوق المالية السعودية مع أعلى معايير الأمان والحماية" : 
                        "Licensed by Saudi Capital Market Authority with highest security and protection standards")
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6-10V7a2 2 0 11-4 0V3a2 2 0 114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "متوافقة مع الشريعة" : "Sharia Compliant")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "جميع المنتجات والخدمات متوافقة مع أحكام الشريعة الإسلامية" : 
                        "All products and services are compliant with Islamic Sharia principles")
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "عوائد مجزية" : "Attractive Returns")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "عوائد تنافسية تصل إلى 15% سنوياً مع مخاطر محسوبة" : 
                        "Competitive returns up to 15% annually with calculated risks")
                </p>
            </div>
            
            <!-- Feature 4 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "شفافية كاملة" : "Full Transparency")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "شفافية كاملة في جميع العمليات والاستثمارات مع تقارير دورية" : 
                        "Complete transparency in all operations and investments with regular reports")
                </p>
            </div>
            
            <!-- Feature 5 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "دعم متخصص" : "Expert Support")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "فريق متخصص من الخبراء المالييين لدعمك في رحلة الاستثمار" : 
                        "Specialized team of financial experts to support your investment journey")
                </p>
            </div>
            
            <!-- Feature 6 -->
            <div class="card card-hover animate-on-scroll @(isRtl ? "text-right" : "text-left")">
                <div class="feature-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "منصة سهلة الاستخدام" : "User-Friendly Platform")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "منصة رقمية سهلة الاستخدام مع تطبيق جوال متطور" : 
                        "Easy-to-use digital platform with advanced mobile application")
                </p>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                @(isArabic ? "كيف يعمل؟" : "How It Works?")
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                @(isArabic ? 
                    "عملية بسيطة وآمنة للاستثمار والحصول على التمويل" : 
                    "Simple and secure process for investing and getting funding")
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="text-center animate-on-scroll">
                <div class="w-20 h-20 bg-mdd-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">1</div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "إنشاء حساب" : "Create Account")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "سجل حساباً جديداً وأكمل عملية التحقق بخطوات بسيطة" : 
                        "Register a new account and complete verification with simple steps")
                </p>
            </div>
            
            <!-- Step 2 -->
            <div class="text-center animate-on-scroll">
                <div class="w-20 h-20 bg-mdd-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">2</div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "اختر الاستثمار" : "Choose Investment")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "تصفح الفرص الاستثمارية المتاحة واختر ما يناسبك" : 
                        "Browse available investment opportunities and choose what suits you")
                </p>
            </div>
            
            <!-- Step 3 -->
            <div class="text-center animate-on-scroll">
                <div class="w-20 h-20 bg-mdd-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">3</div>
                <h3 class="text-xl font-bold mb-4">@(isArabic ? "ابدأ الاستثمار" : "Start Investing")</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @(isArabic ? 
                        "استثمر بالمبلغ المناسب وتابع عوائدك بشكل دوري" : 
                        "Invest with the suitable amount and track your returns regularly")
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 hero-gradient">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-4xl mx-auto text-white animate-on-scroll">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                @(isArabic ? "ابدأ رحلة الاستثمار اليوم" : "Start Your Investment Journey Today")
            </h2>
            <p class="text-xl mb-8 text-gray-200">
                @(isArabic ? 
                    "انضم إلى آلاف المستثمرين الذين يحققون عوائد مجزية من خلال منصتنا" : 
                    "Join thousands of investors achieving attractive returns through our platform")
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/register" class="btn-primary text-lg px-8 py-4 bg-white text-mdd-primary hover:bg-gray-100">
                    @(isArabic ? "ابدأ الآن" : "Get Started")
                </a>
                <a href="/about" class="btn-secondary text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-mdd-primary">
                    @(isArabic ? "تعرف أكثر" : "Learn More")
                </a>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Initialize counter animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation classes to elements
            const animateElements = document.querySelectorAll('.animate-on-scroll');
            animateElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
            });
            
            // Trigger animations when elements come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.transition = 'all 0.6s ease-out';
                    }
                });
            }, { threshold: 0.1 });
            
            animateElements.forEach(el => observer.observe(el));
        });
    </script>
}
