using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;
using System.Collections.Generic;
using System.Linq;

namespace MddPlus.Models.DocumentTypes
{
    public class HomePageModel : BasePageModel
    {
        public HomePageModel(IPublishedContent content) : base(content) { }

        // Hero Section
        public string HeroTitleAr => this.Value<string>("heroTitleAr") ?? "منصة التمويل الجماعي المتوافقة مع الشريعة";
        public string HeroTitleEn => this.Value<string>("heroTitleEn") ?? "Sharia-Compliant Crowdfunding Platform";
        public string HeroSubtitleAr => this.Value<string>("heroSubtitleAr") ?? "نربط المستثمرين والمقترضين من خلال حلول مالية مبتكرة";
        public string HeroSubtitleEn => this.Value<string>("heroSubtitleEn") ?? "Connecting investors and borrowers through innovative financial solutions";
        public IPublishedContent HeroImage => this.Value<IPublishedContent>("heroImage");
        public IPublishedContent HeroVideo => this.Value<IPublishedContent>("heroVideo");
        
        // Trust Indicators
        public string TrustIndicator1Ar => this.Value<string>("trustIndicator1Ar") ?? "مرخصة من هيئة السوق المالية";
        public string TrustIndicator1En => this.Value<string>("trustIndicator1En") ?? "Licensed by CMA";
        public string TrustIndicator2Ar => this.Value<string>("trustIndicator2Ar") ?? "متوافقة مع الشريعة الإسلامية";
        public string TrustIndicator2En => this.Value<string>("trustIndicator2En") ?? "Sharia Compliant";
        public string TrustIndicator3Ar => this.Value<string>("trustIndicator3Ar") ?? "آمنة ومضمونة 100%";
        public string TrustIndicator3En => this.Value<string>("trustIndicator3En") ?? "100% Secure & Guaranteed";
        
        // Statistics
        public IEnumerable<StatisticItemModel> Statistics => this.Value<IEnumerable<IPublishedContent>>("statistics")?.Select(x => new StatisticItemModel(x)) ?? Enumerable.Empty<StatisticItemModel>();
        
        // Features
        public IEnumerable<FeatureItemModel> Features => this.Value<IEnumerable<IPublishedContent>>("features")?.Select(x => new FeatureItemModel(x)) ?? Enumerable.Empty<FeatureItemModel>();
        
        // CTA Section
        public string CtaTitleAr => this.Value<string>("ctaTitleAr") ?? "ابدأ رحلة الاستثمار الذكي اليوم";
        public string CtaTitleEn => this.Value<string>("ctaTitleEn") ?? "Start Your Smart Investment Journey Today";
        public string CtaSubtitleAr => this.Value<string>("ctaSubtitleAr") ?? "انضم إلى أكثر من 2500 مستثمر يحققون عوائد مجزية";
        public string CtaSubtitleEn => this.Value<string>("ctaSubtitleEn") ?? "Join over 2,500 investors achieving attractive returns";
        public string CtaButton1TextAr => this.Value<string>("ctaButton1TextAr") ?? "ابدأ الاستثمار الآن";
        public string CtaButton1TextEn => this.Value<string>("ctaButton1TextEn") ?? "Start Investing Now";
        public string CtaButton1Link => this.Value<string>("ctaButton1Link") ?? "/investors";
        public string CtaButton2TextAr => this.Value<string>("ctaButton2TextAr") ?? "تعرف على المنصة";
        public string CtaButton2TextEn => this.Value<string>("ctaButton2TextEn") ?? "Learn About Platform";
        public string CtaButton2Link => this.Value<string>("ctaButton2Link") ?? "/about";
        
        // Helper Methods
        public string GetHeroTitle(string language = "ar")
        {
            return language == "ar" ? HeroTitleAr : HeroTitleEn;
        }
        
        public string GetHeroSubtitle(string language = "ar")
        {
            return language == "ar" ? HeroSubtitleAr : HeroSubtitleEn;
        }
        
        public string GetCtaTitle(string language = "ar")
        {
            return language == "ar" ? CtaTitleAr : CtaTitleEn;
        }
        
        public string GetCtaSubtitle(string language = "ar")
        {
            return language == "ar" ? CtaSubtitleAr : CtaSubtitleEn;
        }
        
        public string GetCtaButton1Text(string language = "ar")
        {
            return language == "ar" ? CtaButton1TextAr : CtaButton1TextEn;
        }
        
        public string GetCtaButton2Text(string language = "ar")
        {
            return language == "ar" ? CtaButton2TextAr : CtaButton2TextEn;
        }
    }
}
