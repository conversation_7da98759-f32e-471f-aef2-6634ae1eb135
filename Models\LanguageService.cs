using System.Globalization;

namespace MddPlus.Models
{
    public class LanguageService
    {
        private readonly Dictionary<string, Dictionary<string, string>> _translations;

        public LanguageService()
        {
            _translations = new Dictionary<string, Dictionary<string, string>>
            {
                ["ar"] = new Dictionary<string, string>
                {
                    // Navigation
                    ["nav.home"] = "الرئيسية",
                    ["nav.about"] = "من نحن",
                    ["nav.services"] = "خدماتنا",
                    ["nav.investors"] = "المستثمرون",
                    ["nav.borrowers"] = "المقترضون",
                    ["nav.faq"] = "الأسئلة الشائعة",
                    ["nav.contact"] = "اتصل بنا",
                    ["nav.login"] = "تسجيل الدخول",
                    ["nav.signup"] = "ابدأ الآن",
                    
                    // Home Page
                    ["home.title"] = "منصة التمويل الجماعي المتوافقة مع الشريعة",
                    ["home.subtitle"] = "نربط المستثمرين والمقترضين من خلال حلول مالية مبتكرة ومتوافقة مع الشريعة الإسلامية، مساهمين في تحقيق رؤية المملكة 2030",
                    ["home.cta.invest"] = "ابدأ الاستثمار",
                    ["home.cta.funding"] = "احصل على تمويل",
                    ["home.trust.licensed"] = "مرخصة من هيئة السوق المالية",
                    ["home.trust.sharia"] = "متوافقة مع الشريعة الإسلامية",
                    ["home.trust.secure"] = "آمنة ومضمونة 100%",
                    
                    // Statistics
                    ["stats.title"] = "أرقام تتحدث عن نفسها",
                    ["stats.subtitle"] = "نتائج متميزة وعوائد مجزية لمستثمرينا",
                    ["stats.funding"] = "مليون ريال",
                    ["stats.funding.desc"] = "إجمالي التمويل المقدم",
                    ["stats.investors"] = "مستثمر",
                    ["stats.investors.desc"] = "مستثمر نشط في المنصة",
                    ["stats.success"] = "معدل نجاح المشاريع",
                    ["stats.return"] = "متوسط العائد السنوي",
                    
                    // Features
                    ["features.title"] = "لماذا تختار مدد بلس؟",
                    ["features.subtitle"] = "نقدم منصة آمنة وموثوقة للتمويل الجماعي المتوافق مع الشريعة الإسلامية، مع ضمانات قوية وعوائد مجزية",
                    ["features.secure.title"] = "آمنة ومرخصة",
                    ["features.secure.desc"] = "مرخصة من هيئة السوق المالية السعودية مع أعلى معايير الأمان والحماية والشفافية",
                    ["features.sharia.title"] = "متوافقة مع الشريعة",
                    ["features.sharia.desc"] = "جميع المنتجات والخدمات متوافقة مع أحكام الشريعة الإسلامية ومعتمدة من الهيئة الشرعية",
                    ["features.returns.title"] = "عوائد مجزية",
                    ["features.returns.desc"] = "عوائد تنافسية تصل إلى 15.8% سنوياً مع ضمانات قوية ومخاطر محسوبة",
                    ["features.transparency.title"] = "شفافية كاملة",
                    ["features.transparency.desc"] = "شفافية كاملة في جميع العمليات والاستثمارات مع تقارير دورية ومتابعة مباشرة",
                    ["features.support.title"] = "دعم متخصص",
                    ["features.support.desc"] = "فريق متخصص من الخبراء الماليين لدعمك في رحلة الاستثمار على مدار الساعة",
                    ["features.platform.title"] = "منصة سهلة الاستخدام",
                    ["features.platform.desc"] = "منصة رقمية سهلة الاستخدام مع تطبيق جوال متطور وواجهة عربية بالكامل",
                    
                    // CTA Section
                    ["cta.title"] = "ابدأ رحلة الاستثمار الذكي اليوم",
                    ["cta.subtitle"] = "انضم إلى أكثر من 2500 مستثمر يحققون عوائد مجزية تصل إلى 15.8% سنوياً من خلال منصتنا المتوافقة مع الشريعة",
                    ["cta.invest"] = "ابدأ الاستثمار الآن",
                    ["cta.learn"] = "تعرف على المنصة",
                    
                    // Footer
                    ["footer.company"] = "منصة التمويل الذكي",
                    ["footer.description"] = "شركة سعودية مرخصة متخصصة في التمويل الجماعي القائم على الدين المتوافق مع الشريعة الإسلامية.",
                    ["footer.quicklinks"] = "روابط سريعة",
                    ["footer.support"] = "الدعم",
                    ["footer.legal"] = "القانونية والامتثال",
                    ["footer.rights"] = "جميع الحقوق محفوظة",
                    ["footer.licensed"] = "مرخصة من هيئة السوق المالية",
                    ["footer.cr"] = "سجل تجاري: **********",
                    
                    // Common
                    ["common.loading"] = "جاري التحميل...",
                    ["common.error"] = "حدث خطأ",
                    ["common.success"] = "تم بنجاح",
                    ["common.cancel"] = "إلغاء",
                    ["common.confirm"] = "تأكيد",
                    ["common.save"] = "حفظ",
                    ["common.edit"] = "تعديل",
                    ["common.delete"] = "حذف",
                    ["common.view"] = "عرض",
                    ["common.download"] = "تحميل",
                    ["common.upload"] = "رفع",
                    ["common.search"] = "بحث",
                    ["common.filter"] = "تصفية",
                    ["common.sort"] = "ترتيب",
                    ["common.next"] = "التالي",
                    ["common.previous"] = "السابق",
                    ["common.close"] = "إغلاق"
                },
                
                ["en"] = new Dictionary<string, string>
                {
                    // Navigation
                    ["nav.home"] = "Home",
                    ["nav.about"] = "About Us",
                    ["nav.services"] = "Services",
                    ["nav.investors"] = "Investors",
                    ["nav.borrowers"] = "Borrowers",
                    ["nav.faq"] = "FAQ",
                    ["nav.contact"] = "Contact",
                    ["nav.login"] = "Login",
                    ["nav.signup"] = "Get Started",
                    
                    // Home Page
                    ["home.title"] = "Sharia-Compliant Crowdfunding Platform",
                    ["home.subtitle"] = "Connecting investors and borrowers through innovative financial solutions compliant with Islamic Sharia, contributing to Saudi Vision 2030",
                    ["home.cta.invest"] = "Start Investing",
                    ["home.cta.funding"] = "Get Funding",
                    ["home.trust.licensed"] = "Licensed by CMA",
                    ["home.trust.sharia"] = "Sharia Compliant",
                    ["home.trust.secure"] = "100% Secure & Guaranteed",
                    
                    // Statistics
                    ["stats.title"] = "Numbers That Speak for Themselves",
                    ["stats.subtitle"] = "Outstanding results and attractive returns for our investors",
                    ["stats.funding"] = "Million SAR",
                    ["stats.funding.desc"] = "Total Funding Provided",
                    ["stats.investors"] = "Investors",
                    ["stats.investors.desc"] = "Active Investors on Platform",
                    ["stats.success"] = "Project Success Rate",
                    ["stats.return"] = "Average Annual Return",
                    
                    // Features
                    ["features.title"] = "Why Choose MDD Plus?",
                    ["features.subtitle"] = "We provide a secure and trusted platform for Sharia-compliant crowdfunding, with strong guarantees and attractive returns",
                    ["features.secure.title"] = "Secure & Licensed",
                    ["features.secure.desc"] = "Licensed by Saudi Capital Market Authority with highest security, protection and transparency standards",
                    ["features.sharia.title"] = "Sharia Compliant",
                    ["features.sharia.desc"] = "All products and services comply with Islamic Sharia principles and are certified by the Sharia Board",
                    ["features.returns.title"] = "Attractive Returns",
                    ["features.returns.desc"] = "Competitive returns up to 15.8% annually with strong guarantees and calculated risks",
                    ["features.transparency.title"] = "Full Transparency",
                    ["features.transparency.desc"] = "Complete transparency in all operations and investments with regular reports and direct monitoring",
                    ["features.support.title"] = "Expert Support",
                    ["features.support.desc"] = "Specialized team of financial experts to support you in your investment journey 24/7",
                    ["features.platform.title"] = "User-Friendly Platform",
                    ["features.platform.desc"] = "Easy-to-use digital platform with advanced mobile app and fully Arabic interface",
                    
                    // CTA Section
                    ["cta.title"] = "Start Your Smart Investment Journey Today",
                    ["cta.subtitle"] = "Join over 2,500 investors achieving attractive returns up to 15.8% annually through our Sharia-compliant platform",
                    ["cta.invest"] = "Start Investing Now",
                    ["cta.learn"] = "Learn About Platform",
                    
                    // Footer
                    ["footer.company"] = "Smart Funding Platform",
                    ["footer.description"] = "Licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding.",
                    ["footer.quicklinks"] = "Quick Links",
                    ["footer.support"] = "Support",
                    ["footer.legal"] = "Legal & Compliance",
                    ["footer.rights"] = "All rights reserved",
                    ["footer.licensed"] = "Licensed by CMA",
                    ["footer.cr"] = "CR: **********",
                    
                    // Common
                    ["common.loading"] = "Loading...",
                    ["common.error"] = "Error occurred",
                    ["common.success"] = "Success",
                    ["common.cancel"] = "Cancel",
                    ["common.confirm"] = "Confirm",
                    ["common.save"] = "Save",
                    ["common.edit"] = "Edit",
                    ["common.delete"] = "Delete",
                    ["common.view"] = "View",
                    ["common.download"] = "Download",
                    ["common.upload"] = "Upload",
                    ["common.search"] = "Search",
                    ["common.filter"] = "Filter",
                    ["common.sort"] = "Sort",
                    ["common.next"] = "Next",
                    ["common.previous"] = "Previous",
                    ["common.close"] = "Close"
                }
            };
        }

        public string GetTranslation(string key, string language = "ar")
        {
            if (_translations.ContainsKey(language) && _translations[language].ContainsKey(key))
            {
                return _translations[language][key];
            }
            
            // Fallback to Arabic if English translation not found
            if (language != "ar" && _translations["ar"].ContainsKey(key))
            {
                return _translations["ar"][key];
            }
            
            return key; // Return key if no translation found
        }

        public Dictionary<string, string> GetAllTranslations(string language = "ar")
        {
            return _translations.ContainsKey(language) ? _translations[language] : _translations["ar"];
        }

        public List<string> GetSupportedLanguages()
        {
            return _translations.Keys.ToList();
        }

        public bool IsRTL(string language)
        {
            return language == "ar";
        }

        public string GetLanguageDirection(string language)
        {
            return IsRTL(language) ? "rtl" : "ltr";
        }

        public string GetFontFamily(string language)
        {
            return language == "ar" ? "Cairo, sans-serif" : "Inter, sans-serif";
        }
    }
}
