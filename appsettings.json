{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "a983af62-5980-4eea-9a14-37e99e5c7993", "SanitizeTinyMce": true, "DefaultUILanguage": "en-US"}, "Content": {"AllowEditInvariantFromNonDefault": true, "ContentVersionCleanupPolicy": {"EnableCleanup": true}, "Multilingual": {"Languages": [{"IsoCode": "en-US", "CultureName": "English (United States)", "IsDefault": true}, {"IsoCode": "ar-SA", "CultureName": "Arabic (Saudi Arabia)", "IsDefault": false}]}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"AllowConcurrentLogins": false}, "ModelsBuilder": {"ModelsMode": "SourceCodeManual"}}}, "ConnectionStrings": {"umbracoDbDSN": "Data Source=|DataDirectory|/Umbraco.sqlite.db;Cache=Shared;Foreign Keys=True;Pooling=True", "umbracoDbDSN_ProviderName": "Microsoft.Data.Sqlite"}}