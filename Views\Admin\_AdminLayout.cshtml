<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <title>@ViewBag.Title - لوحة إدارة مدد بلس</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        'mdd-primary': '#005B82',
                        'mdd-secondary': '#007BA7',
                        'mdd-accent': '#BCBEC0',
                        'mdd-text': '#3C3C3B',
                        'mdd-dark-bg': '#1F2937',
                        'mdd-dark-accent': '#33B5E5'
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    
    <!-- Custom Admin CSS -->
    <link href="/css/admin.css" rel="stylesheet">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Alpine.js for Interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>

<body class="font-arabic bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300" dir="rtl">
    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>
    
    <!-- Sidebar -->
    <aside id="sidebar" class="fixed top-0 right-0 z-50 w-64 h-screen transition-transform transform translate-x-full lg:translate-x-0 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-xl">
        <div class="h-full px-3 py-4 overflow-y-auto">
            <!-- Logo -->
            <div class="flex items-center justify-between mb-8 px-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-mdd-primary to-mdd-secondary rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">م</span>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-gray-900 dark:text-white">مدد بلس</h1>
                        <p class="text-xs text-gray-500 dark:text-gray-400">لوحة الإدارة</p>
                    </div>
                </div>
                <button id="close-sidebar" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Navigation -->
            <nav class="space-y-2">
                <a href="/admin/dashboard" class="admin-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Dashboard" ? "active" : "")">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    <span>لوحة التحكم</span>
                </a>
                
                <div class="admin-nav-group">
                    <div class="admin-nav-group-title">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>إدارة المحتوى</span>
                    </div>
                    <div class="admin-nav-group-items">
                        <a href="/admin/content" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "ContentManagement" ? "active" : "")">
                            📄 جميع الصفحات
                        </a>
                        <a href="/admin/content/home" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "EditHomePage" ? "active" : "")">
                            🏠 الصفحة الرئيسية
                        </a>
                        <a href="/admin/content/services" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "EditServicesPage" ? "active" : "")">
                            💼 صفحة الخدمات
                        </a>
                    </div>
                </div>
                
                <div class="admin-nav-group">
                    <div class="admin-nav-group-title">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                        <span>العناصر</span>
                    </div>
                    <div class="admin-nav-group-items">
                        <a href="/admin/statistics" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "ManageStatistics" ? "active" : "")">
                            📊 الإحصائيات
                        </a>
                        <a href="/admin/features" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "ManageFeatures" ? "active" : "")">
                            ⭐ المميزات
                        </a>
                        <a href="/admin/services" class="admin-nav-sublink @(ViewContext.RouteData.Values["action"]?.ToString() == "ManageServices" ? "active" : "")">
                            🏢 الخدمات
                        </a>
                    </div>
                </div>
                
                <a href="/admin/media" class="admin-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "MediaManagement" ? "active" : "")">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>إدارة الوسائط</span>
                </a>
                
                <a href="/admin/settings" class="admin-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Settings" ? "active" : "")">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>الإعدادات</span>
                </a>
            </nav>
            
            <!-- User Profile -->
            <div class="absolute bottom-4 left-3 right-3">
                <div class="flex items-center p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">م</span>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">المدير</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
                    </div>
                    <button class="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <div class="lg:mr-64">
        <!-- Top Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-30">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    
                    <!-- Page Title -->
                    <div class="flex-1 lg:flex-none">
                        <h1 class="text-xl font-bold text-gray-900 dark:text-white">@ViewBag.Title</h1>
                    </div>
                    
                    <!-- Header Actions -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Theme Toggle -->
                        <button id="theme-toggle" class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <svg class="w-5 h-5 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
                        
                        <!-- Notifications -->
                        <button class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </button>
                        
                        <!-- Preview Site -->
                        <a href="/" target="_blank" class="btn-primary text-sm px-4 py-2">
                            👁️ معاينة الموقع
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page Content -->
        <main class="p-4 sm:p-6 lg:p-8">
            @RenderBody()
        </main>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3 shadow-xl">
            <div class="loading-spinner"></div>
            <span class="text-gray-700 dark:text-gray-300">جاري التحميل...</span>
        </div>
    </div>
    
    <!-- Success Toast -->
    <div id="success-toast" class="fixed top-4 left-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 z-50">
        <div class="flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span id="success-message">تم الحفظ بنجاح</span>
        </div>
    </div>
    
    <!-- Error Toast -->
    <div id="error-toast" class="fixed top-4 left-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 z-50">
        <div class="flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span id="error-message">حدث خطأ</span>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/js/admin.js"></script>
    @RenderSection("Scripts", required: false)
</body>
</html>
