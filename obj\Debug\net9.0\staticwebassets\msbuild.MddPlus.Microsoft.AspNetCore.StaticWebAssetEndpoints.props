﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/MddPlus/css/main.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\main.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-d08nOXpHiEUhvmUy6eDWpVjuDuLoxYT3ZYmt75jyrsE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6028"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d08nOXpHiEUhvmUy6eDWpVjuDuLoxYT3ZYmt75jyrsE=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:36:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/css/main.zd4t40puo8.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\main.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zd4t40puo8"},{"Name":"integrity","Value":"sha256-d08nOXpHiEUhvmUy6eDWpVjuDuLoxYT3ZYmt75jyrsE="},{"Name":"label","Value":"_content/MddPlus/css/main.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6028"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d08nOXpHiEUhvmUy6eDWpVjuDuLoxYT3ZYmt75jyrsE=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:36:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/css/rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PYZPUDeiI9zwYhwLJwfGnuJ3W/BB\u002BZsMzg8occ/qCdw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5680"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PYZPUDeiI9zwYhwLJwfGnuJ3W/BB\u002BZsMzg8occ/qCdw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/css/rtl.fd2jbrndyl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fd2jbrndyl"},{"Name":"integrity","Value":"sha256-PYZPUDeiI9zwYhwLJwfGnuJ3W/BB\u002BZsMzg8occ/qCdw="},{"Name":"label","Value":"_content/MddPlus/css/rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5680"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PYZPUDeiI9zwYhwLJwfGnuJ3W/BB\u002BZsMzg8occ/qCdw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/MddPlus/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:32:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:32:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/images/placeholder.qr04p7f6yd.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\placeholder.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qr04p7f6yd"},{"Name":"integrity","Value":"sha256-Gn60R6bbOuyZiG0FF2G4LFeyAI1HUl46SjbP8r0YqbQ="},{"Name":"label","Value":"_content/MddPlus/images/placeholder.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"920"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Gn60R6bbOuyZiG0FF2G4LFeyAI1HUl46SjbP8r0YqbQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:41:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/images/placeholder.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\placeholder.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Gn60R6bbOuyZiG0FF2G4LFeyAI1HUl46SjbP8r0YqbQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"920"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Gn60R6bbOuyZiG0FF2G4LFeyAI1HUl46SjbP8r0YqbQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:41:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/language-switcher.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\language-switcher.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sjJYhpAKqt0IWMuka6EsBjuV7MAr0H4QnLg3bbGfqmk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6342"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022sjJYhpAKqt0IWMuka6EsBjuV7MAr0H4QnLg3bbGfqmk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:59 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/language-switcher.mvkcu4l2rs.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\language-switcher.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mvkcu4l2rs"},{"Name":"integrity","Value":"sha256-sjJYhpAKqt0IWMuka6EsBjuV7MAr0H4QnLg3bbGfqmk="},{"Name":"label","Value":"_content/MddPlus/js/language-switcher.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6342"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022sjJYhpAKqt0IWMuka6EsBjuV7MAr0H4QnLg3bbGfqmk=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:59 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/main.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ETODyln1VMxArxltiGD2eogcuRTU3aMzJsVRbMx5Dj8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"17475"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ETODyln1VMxArxltiGD2eogcuRTU3aMzJsVRbMx5Dj8=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:38:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/main.pma95olrw0.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pma95olrw0"},{"Name":"integrity","Value":"sha256-ETODyln1VMxArxltiGD2eogcuRTU3aMzJsVRbMx5Dj8="},{"Name":"label","Value":"_content/MddPlus/js/main.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17475"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ETODyln1VMxArxltiGD2eogcuRTU3aMzJsVRbMx5Dj8=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:38:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/theme-toggle.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\theme-toggle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xi1b83X9nbZ3fIXhQoyUNhjwGczKNXziqak\u002BEIlEJd4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4287"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xi1b83X9nbZ3fIXhQoyUNhjwGczKNXziqak\u002BEIlEJd4=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MddPlus/js/theme-toggle.mkiv6matph.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\theme-toggle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mkiv6matph"},{"Name":"integrity","Value":"sha256-xi1b83X9nbZ3fIXhQoyUNhjwGczKNXziqak\u002BEIlEJd4="},{"Name":"label","Value":"_content/MddPlus/js/theme-toggle.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4287"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xi1b83X9nbZ3fIXhQoyUNhjwGczKNXziqak\u002BEIlEJd4=\u0022"},{"Name":"Last-Modified","Value":"Mon, 14 Jul 2025 13:37:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>