<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="b2c3d4e5-f6g7-8901-bcde-f23456789012" Alias="homePageModel" Level="2">
  <Info>
    <Name>Home Page Model</Name>
    <Icon>icon-home</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Home page with hero section, statistics, and features</Description>
    <AllowAtRoot>True</AllowAtRoot>
    <IsListView>False</IsListView>
    <Variations>Nothing</Variations>
    <IsElement>false</IsElement>
    <Parent Key="a1b2c3d4-e5f6-7890-abcd-ef1234567890">basePageModel</Parent>
  </Info>
  <Structure />
  <GenericProperties>
    <!-- Hero Section -->
    <GenericProperty>
      <Key>hero-title-ar</Key>
      <Name>Hero Title (Arabic)</Name>
      <Alias>heroTitleAr</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main hero title in Arabic]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="hero">Hero Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>hero-title-en</Key>
      <Name>Hero Title (English)</Name>
      <Alias>heroTitleEn</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main hero title in English]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="hero">Hero Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>hero-subtitle-ar</Key>
      <Name>Hero Subtitle (Arabic)</Name>
      <Alias>heroSubtitleAr</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hero subtitle/description in Arabic]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="hero">Hero Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>hero-subtitle-en</Key>
      <Name>Hero Subtitle (English)</Name>
      <Alias>heroSubtitleEn</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hero subtitle/description in English]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="hero">Hero Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Statistics -->
    <GenericProperty>
      <Key>statistics</Key>
      <Name>Statistics</Name>
      <Alias>statistics</Alias>
      <Definition>b4471851-82b6-4c75-afa4-39fa9c6a75e9</Definition>
      <Type>Umbraco.NestedContent</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Statistics to display on the home page]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="statistics">Statistics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Features -->
    <GenericProperty>
      <Key>features</Key>
      <Name>Features</Name>
      <Alias>features</Alias>
      <Definition>b4471851-82b6-4c75-afa4-39fa9c6a75e9</Definition>
      <Type>Umbraco.NestedContent</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Features to display on the home page]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="features">Features</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- CTA Section -->
    <GenericProperty>
      <Key>cta-title-ar</Key>
      <Name>CTA Title (Arabic)</Name>
      <Alias>ctaTitleAr</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Call to action title in Arabic]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="cta">Call to Action</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>cta-title-en</Key>
      <Name>CTA Title (English)</Name>
      <Alias>ctaTitleEn</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Call to action title in English]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="cta">Call to Action</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>cta-button1-text-ar</Key>
      <Name>CTA Button 1 Text (Arabic)</Name>
      <Alias>ctaButton1TextAr</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[First CTA button text in Arabic]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="cta">Call to Action</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>cta-button1-text-en</Key>
      <Name>CTA Button 1 Text (English)</Name>
      <Alias>ctaButton1TextEn</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[First CTA button text in English]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="cta">Call to Action</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>cta-button1-link</Key>
      <Name>CTA Button 1 Link</Name>
      <Alias>ctaButton1Link</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[First CTA button link URL]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="cta">Call to Action</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>hero-tab</Key>
      <Caption>Hero Section</Caption>
      <Alias>hero</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>statistics-tab</Key>
      <Caption>Statistics</Caption>
      <Alias>statistics</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>features-tab</Key>
      <Caption>Features</Caption>
      <Alias>features</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
    <Tab>
      <Key>cta-tab</Key>
      <Caption>Call to Action</Caption>
      <Alias>cta</Alias>
      <Type>Tab</Type>
      <SortOrder>3</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
