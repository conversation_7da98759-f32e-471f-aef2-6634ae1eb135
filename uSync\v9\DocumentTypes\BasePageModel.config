<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="a1b2c3d4-e5f6-7890-abcd-ef1234567890" Alias="basePageModel" Level="1">
  <Info>
    <Name>Base Page Model</Name>
    <Icon>icon-document</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Base page model with common properties for all pages</Description>
    <AllowAtRoot>True</AllowAtRoot>
    <IsListView>False</IsListView>
    <Variations>Nothing</Variations>
    <IsElement>false</IsElement>
  </Info>
  <Structure />
  <GenericProperties>
    <!-- SEO Properties -->
    <GenericProperty>
      <Key>meta-title</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title for the page]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>meta-description</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description for the page]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>meta-keywords</Key>
      <Name>Meta Keywords</Name>
      <Alias>metaKeywords</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta keywords for the page]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Multilingual Properties -->
    <GenericProperty>
      <Key>title-ar</Key>
      <Name>Title (Arabic)</Name>
      <Alias>titleAr</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page title in Arabic]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>title-en</Key>
      <Name>Title (English)</Name>
      <Alias>titleEn</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page title in English]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>description-ar</Key>
      <Name>Description (Arabic)</Name>
      <Alias>descriptionAr</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page description in Arabic]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>description-en</Key>
      <Name>Description (English)</Name>
      <Alias>descriptionEn</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page description in English]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Navigation Properties -->
    <GenericProperty>
      <Key>hide-from-navigation</Key>
      <Name>Hide from Navigation</Name>
      <Alias>hideFromNavigation</Alias>
      <Definition>92897bc6-a5f3-4ffe-ae27-f2e7e33dda49</Definition>
      <Type>Umbraco.TrueFalse</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hide this page from navigation menus]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="navigation">Navigation</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <GenericProperty>
      <Key>navigation-order</Key>
      <Name>Navigation Order</Name>
      <Alias>navigationOrder</Alias>
      <Definition>2e6d3631-066e-44b8-aec4-96f09099b2b5</Definition>
      <Type>Umbraco.Integer</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Order in navigation menu (lower numbers appear first)]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="navigation">Navigation</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>content-tab</Key>
      <Caption>Content</Caption>
      <Alias>content</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>seo-tab</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>navigation-tab</Key>
      <Caption>Navigation</Caption>
      <Alias>navigation</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
