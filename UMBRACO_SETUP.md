# 🏗️ **إعداد المحتوى الديناميكي في Umbraco**

## 📋 **خطوات الإعداد**

### **1. إنشاء Document Types في Umbraco**

#### **أ. Base Page Model**
1. اذهب إلى **Settings** → **Document Types**
2. انقر **Create** → **Document Type**
3. اسم: `Base Page Model`
4. Alias: `basePageModel`
5. Icon: `icon-document`

**Properties:**
- **Content Tab:**
  - `titleAr` (Textstring) - Title (Arabic) - Required
  - `titleEn` (Textstring) - Title (English) - Required  
  - `descriptionAr` (Textarea) - Description (Arabic)
  - `descriptionEn` (Textarea) - Description (English)

- **SEO Tab:**
  - `metaTitle` (Textstring) - Meta Title
  - `metaDescription` (Textarea) - Meta Description
  - `metaKeywords` (Textstring) - Meta Keywords

- **Navigation Tab:**
  - `hideFromNavigation` (True/False) - Hide from Navigation
  - `navigationOrder` (Numeric) - Navigation Order

#### **ب. Home Page Model**
1. انقر **Create** → **Document Type**
2. اسم: `Home Page Model`
3. Alias: `homePageModel`
4. Icon: `icon-home`
5. **Master**: اختر `Base Page Model`

**Properties:**
- **Hero Section Tab:**
  - `heroTitleAr` (Textstring) - Hero Title (Arabic) - Required
  - `heroTitleEn` (Textstring) - Hero Title (English) - Required
  - `heroSubtitleAr` (Textarea) - Hero Subtitle (Arabic) - Required
  - `heroSubtitleEn` (Textarea) - Hero Subtitle (English) - Required

- **Statistics Tab:**
  - `statistics` (Nested Content) - Statistics
    - Content Types: `Statistic Item`

- **Features Tab:**
  - `features` (Nested Content) - Features
    - Content Types: `Feature Item`

- **Call to Action Tab:**
  - `ctaTitleAr` (Textstring) - CTA Title (Arabic)
  - `ctaTitleEn` (Textstring) - CTA Title (English)
  - `ctaSubtitleAr` (Textarea) - CTA Subtitle (Arabic)
  - `ctaSubtitleEn` (Textarea) - CTA Subtitle (English)
  - `ctaButton1TextAr` (Textstring) - CTA Button 1 Text (Arabic)
  - `ctaButton1TextEn` (Textstring) - CTA Button 1 Text (English)
  - `ctaButton1Link` (Textstring) - CTA Button 1 Link
  - `ctaButton2TextAr` (Textstring) - CTA Button 2 Text (Arabic)
  - `ctaButton2TextEn` (Textstring) - CTA Button 2 Text (English)
  - `ctaButton2Link` (Textstring) - CTA Button 2 Link

### **2. إنشاء Element Types للـ Nested Content**

#### **أ. Statistic Item**
1. انقر **Create** → **Element Type**
2. اسم: `Statistic Item`
3. Alias: `statisticItem`
4. Icon: `icon-chart`

**Properties:**
- `number` (Textstring) - Number - Required
- `unit` (Textstring) - Unit
- `labelAr` (Textstring) - Label (Arabic) - Required
- `labelEn` (Textstring) - Label (English) - Required
- `descriptionAr` (Textstring) - Description (Arabic)
- `descriptionEn` (Textstring) - Description (English)
- `color` (Dropdown) - Color
  - Options: blue, green, purple, orange, red, indigo
- `icon` (Textstring) - Icon (Emoji)
- `animationDelay` (Numeric) - Animation Delay (ms)
- `progressPercentage` (Numeric) - Progress Percentage (0-100)

#### **ب. Feature Item**
1. انقر **Create** → **Element Type**
2. اسم: `Feature Item`
3. Alias: `featureItem`
4. Icon: `icon-star`

**Properties:**
- `titleAr` (Textstring) - Title (Arabic) - Required
- `titleEn` (Textstring) - Title (English) - Required
- `descriptionAr` (Textarea) - Description (Arabic) - Required
- `descriptionEn` (Textarea) - Description (English) - Required
- `icon` (Textstring) - Icon (Emoji)
- `color` (Dropdown) - Color
  - Options: blue, green, purple, orange, red, indigo, yellow, pink
- `image` (Media Picker) - Image
- `linkUrl` (Textstring) - Link URL
- `linkTextAr` (Textstring) - Link Text (Arabic)
- `linkTextEn` (Textstring) - Link Text (English)
- `isHighlighted` (True/False) - Is Highlighted
- `sortOrder` (Numeric) - Sort Order

### **3. إعداد Nested Content Data Types**

#### **أ. Statistics Data Type**
1. اذهب إلى **Settings** → **Data Types**
2. انقر **Create** → **Data Type**
3. اسم: `Statistics`
4. Property Editor: `Nested Content`
5. **Content Types**: اختر `Statistic Item`
6. **Min Items**: 0
7. **Max Items**: 10

#### **ب. Features Data Type**
1. انقر **Create** → **Data Type**
2. اسم: `Features`
3. Property Editor: `Nested Content`
4. **Content Types**: اختر `Feature Item`
5. **Min Items**: 0
6. **Max Items**: 10

### **4. إنشاء المحتوى**

#### **أ. إنشاء الصفحة الرئيسية**
1. اذهب إلى **Content**
2. انقر **Create** → **Home Page Model**
3. اسم: `الصفحة الرئيسية`

**ملء البيانات:**

**Content Tab:**
- Title (Arabic): `مدد بلس - منصة التمويل الجماعي المتوافقة مع الشريعة`
- Title (English): `MDD Plus - Sharia-Compliant Crowdfunding Platform`
- Description (Arabic): `شركة سعودية مرخصة متخصصة في التمويل الجماعي`
- Description (English): `Licensed Saudi company specializing in crowdfunding`

**Hero Section:**
- Hero Title (Arabic): `منصة التمويل الجماعي المتوافقة مع الشريعة`
- Hero Title (English): `Sharia-Compliant Crowdfunding Platform`
- Hero Subtitle (Arabic): `نربط المستثمرين والمقترضين من خلال حلول مالية مبتكرة`
- Hero Subtitle (English): `Connecting investors and borrowers through innovative solutions`

**Statistics:**
انقر **Add** وأضف:

1. **Statistic 1:**
   - Number: `750`
   - Unit: ` `
   - Label (Arabic): `مليون ريال`
   - Label (English): `Million SAR`
   - Description (Arabic): `إجمالي التمويل المقدم`
   - Description (English): `Total Funding Provided`
   - Color: `blue`
   - Icon: `💰`
   - Animation Delay: `100`
   - Progress Percentage: `75`

2. **Statistic 2:**
   - Number: `2,500`
   - Label (Arabic): `مستثمر`
   - Label (English): `Investors`
   - Description (Arabic): `مستثمر نشط في المنصة`
   - Description (English): `Active Investors`
   - Color: `green`
   - Icon: `👥`
   - Animation Delay: `200`
   - Progress Percentage: `85`

3. **Statistic 3:**
   - Number: `98`
   - Unit: `%`
   - Label (Arabic): `معدل نجاح`
   - Label (English): `Success Rate`
   - Description (Arabic): `معدل نجاح المشاريع`
   - Description (English): `Project Success Rate`
   - Color: `purple`
   - Icon: `📈`
   - Animation Delay: `300`
   - Progress Percentage: `98`

4. **Statistic 4:**
   - Number: `15.8`
   - Unit: `%`
   - Label (Arabic): `عائد سنوي`
   - Label (English): `Annual Return`
   - Description (Arabic): `متوسط العائد السنوي`
   - Description (English): `Average Annual Return`
   - Color: `orange`
   - Icon: `💹`
   - Animation Delay: `400`
   - Progress Percentage: `90`

**Features:**
انقر **Add** وأضف:

1. **Feature 1:**
   - Title (Arabic): `آمنة ومرخصة`
   - Title (English): `Secure & Licensed`
   - Description (Arabic): `مرخصة من هيئة السوق المالية السعودية`
   - Description (English): `Licensed by Saudi Capital Market Authority`
   - Icon: `🛡️`
   - Color: `blue`
   - Is Highlighted: `True`
   - Sort Order: `1`

2. **Feature 2:**
   - Title (Arabic): `متوافقة مع الشريعة`
   - Title (English): `Sharia Compliant`
   - Description (Arabic): `جميع المنتجات متوافقة مع الشريعة الإسلامية`
   - Description (English): `All products comply with Islamic Sharia`
   - Icon: `☪️`
   - Color: `green`
   - Is Highlighted: `True`
   - Sort Order: `2`

3. **Feature 3:**
   - Title (Arabic): `عوائد مجزية`
   - Title (English): `Attractive Returns`
   - Description (Arabic): `عوائد تنافسية تصل إلى 15.8% سنوياً`
   - Description (English): `Competitive returns up to 15.8% annually`
   - Icon: `📈`
   - Color: `purple`
   - Is Highlighted: `False`
   - Sort Order: `3`

**Call to Action:**
- CTA Title (Arabic): `ابدأ رحلة الاستثمار الذكي اليوم`
- CTA Title (English): `Start Your Smart Investment Journey Today`
- CTA Subtitle (Arabic): `انضم إلى أكثر من 2500 مستثمر`
- CTA Subtitle (English): `Join over 2,500 investors`
- CTA Button 1 Text (Arabic): `ابدأ الاستثمار الآن`
- CTA Button 1 Text (English): `Start Investing Now`
- CTA Button 1 Link: `/investors`
- CTA Button 2 Text (Arabic): `تعرف على المنصة`
- CTA Button 2 Text (English): `Learn About Platform`
- CTA Button 2 Link: `/about`

### **5. إعداد Templates**

1. اذهب إلى **Settings** → **Templates**
2. انقر **Create** → **Template**
3. اسم: `Home Page Model`
4. Alias: `homePageModel`
5. **Master Template**: اختر `_Layout`

### **6. ربط Document Type بـ Template**

1. اذهب إلى **Settings** → **Document Types** → **Home Page Model**
2. في تبويب **Settings**
3. **Allowed Templates**: اختر `Home Page Model`
4. **Default Template**: اختر `Home Page Model`
5. احفظ

### **7. تحديث الـ Views**

الآن يمكنك استخدام المحتوى الديناميكي في الـ Views:

```csharp
@model HomePageModel
@{
    var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
}

<h1>@Model.GetHeroTitle(currentLanguage)</h1>
<p>@Model.GetHeroSubtitle(currentLanguage)</p>

@foreach (var statistic in Model.Statistics)
{
    <div class="statistic">
        <span class="number">@<EMAIL></span>
        <span class="label">@statistic.GetLabel(currentLanguage)</span>
        <span class="description">@statistic.GetDescription(currentLanguage)</span>
    </div>
}
```

## 🎯 **النتيجة**

بعد إكمال هذه الخطوات، ستحصل على:

✅ **محتوى ديناميكي بالكامل** يمكن تعديله من Umbraco  
✅ **دعم متعدد اللغات** مع إدارة سهلة للمحتوى  
✅ **مرونة في التحكم** بجميع عناصر الصفحة  
✅ **سهولة الصيانة** والتحديث بدون تعديل الكود  
✅ **إدارة محتوى احترافية** من خلال واجهة Umbraco  

## 🔄 **التحديث والصيانة**

- **إضافة إحصائية جديدة**: من Content → Statistics → Add
- **تعديل النصوص**: مباشرة من Content Editor
- **إضافة ميزة جديدة**: من Content → Features → Add
- **تغيير الألوان والأيقونات**: من خصائص العنصر

**الآن موقع مدد بلس أصبح ديناميكي بالكامل! 🚀**
