using MddPlus.Models.Admin;
using System.Text.Json;

namespace MddPlus.Services
{
    public class ContentManagementService
    {
        private readonly ILogger<ContentManagementService> _logger;
        private readonly string _dataPath;

        public ContentManagementService(ILogger<ContentManagementService> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _dataPath = Path.Combine(environment.ContentRootPath, "Data");
            
            // Ensure data directory exists
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }
        }

        // Dashboard Methods
        public int GetTotalPagesCount()
        {
            return 5; // Home, Services, About, Contact, etc.
        }

        public int GetTotalStatisticsCount()
        {
            var homeContent = GetHomePageContent();
            return homeContent.Statistics.Count;
        }

        public int GetTotalFeaturesCount()
        {
            var homeContent = GetHomePageContent();
            return homeContent.Features.Count;
        }

        public int GetTotalServiceCategoriesCount()
        {
            var servicesContent = GetServicesPageContent();
            return servicesContent.ServiceCategories.Count;
        }

        public List<ActivityLogModel> GetRecentActivities()
        {
            return new List<ActivityLogModel>
            {
                new ActivityLogModel
                {
                    Id = 1,
                    Action = "تحديث المحتوى",
                    Description = "تم تحديث الصفحة الرئيسية",
                    UserName = "المدير",
                    CreatedAt = DateTime.Now.AddMinutes(-5),
                    Icon = "📝",
                    Color = "blue"
                },
                new ActivityLogModel
                {
                    Id = 2,
                    Action = "إضافة إحصائية",
                    Description = "تم إضافة إحصائية جديدة",
                    UserName = "المدير",
                    CreatedAt = DateTime.Now.AddMinutes(-15),
                    Icon = "📊",
                    Color = "green"
                },
                new ActivityLogModel
                {
                    Id = 3,
                    Action = "رفع صورة",
                    Description = "تم رفع صورة جديدة للمعرض",
                    UserName = "المدير",
                    CreatedAt = DateTime.Now.AddHours(-1),
                    Icon = "🖼️",
                    Color = "purple"
                }
            };
        }

        public SystemStatusModel GetSystemStatus()
        {
            return new SystemStatusModel
            {
                IsOnline = true,
                Version = "1.0.0",
                LastBackup = DateTime.Now.AddDays(-1),
                DatabaseSize = 1024 * 1024 * 50, // 50MB
                ActiveUsers = 1,
                CpuUsage = 25.5,
                MemoryUsage = 45.2
            };
        }

        // Content Management Methods
        public List<PageSummaryModel> GetAllPages()
        {
            return new List<PageSummaryModel>
            {
                new PageSummaryModel
                {
                    Id = 1,
                    Name = "الصفحة الرئيسية",
                    Type = "Home Page",
                    Status = "منشورة",
                    LastModified = DateTime.Now.AddHours(-2),
                    ModifiedBy = "المدير",
                    IsPublished = true,
                    Url = "/"
                },
                new PageSummaryModel
                {
                    Id = 2,
                    Name = "صفحة الخدمات",
                    Type = "Services Page",
                    Status = "منشورة",
                    LastModified = DateTime.Now.AddDays(-1),
                    ModifiedBy = "المدير",
                    IsPublished = true,
                    Url = "/services"
                },
                new PageSummaryModel
                {
                    Id = 3,
                    Name = "من نحن",
                    Type = "About Page",
                    Status = "مسودة",
                    LastModified = DateTime.Now.AddDays(-3),
                    ModifiedBy = "المدير",
                    IsPublished = false,
                    Url = "/about"
                }
            };
        }

        public HomePageContentModel GetHomePageContent()
        {
            var filePath = Path.Combine(_dataPath, "home-content.json");
            
            if (File.Exists(filePath))
            {
                try
                {
                    var json = File.ReadAllText(filePath);
                    var content = JsonSerializer.Deserialize<HomePageContentModel>(json);
                    return content ?? GetDefaultHomeContent();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading home page content");
                }
            }
            
            return GetDefaultHomeContent();
        }

        public async Task SaveHomePageContent(HomePageContentModel model)
        {
            var filePath = Path.Combine(_dataPath, "home-content.json");
            
            try
            {
                var json = JsonSerializer.Serialize(model, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("Home page content saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving home page content");
                throw;
            }
        }

        public ServicesPageContentModel GetServicesPageContent()
        {
            var filePath = Path.Combine(_dataPath, "services-content.json");
            
            if (File.Exists(filePath))
            {
                try
                {
                    var json = File.ReadAllText(filePath);
                    var content = JsonSerializer.Deserialize<ServicesPageContentModel>(json);
                    return content ?? GetDefaultServicesContent();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading services page content");
                }
            }
            
            return GetDefaultServicesContent();
        }

        public async Task SaveServicesPageContent(ServicesPageContentModel model)
        {
            var filePath = Path.Combine(_dataPath, "services-content.json");
            
            try
            {
                var json = JsonSerializer.Serialize(model, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("Services page content saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving services page content");
                throw;
            }
        }

        // Statistics Management
        public List<StatisticItemContentModel> GetAllStatistics()
        {
            var homeContent = GetHomePageContent();
            return homeContent.Statistics;
        }

        public async Task SaveStatistic(StatisticItemContentModel model)
        {
            var homeContent = GetHomePageContent();
            
            if (model.Id == 0)
            {
                // Add new statistic
                model.Id = homeContent.Statistics.Count > 0 ? homeContent.Statistics.Max(s => s.Id) + 1 : 1;
                homeContent.Statistics.Add(model);
            }
            else
            {
                // Update existing statistic
                var existingIndex = homeContent.Statistics.FindIndex(s => s.Id == model.Id);
                if (existingIndex >= 0)
                {
                    homeContent.Statistics[existingIndex] = model;
                }
            }
            
            await SaveHomePageContent(homeContent);
        }

        public async Task DeleteStatistic(int id)
        {
            var homeContent = GetHomePageContent();
            homeContent.Statistics.RemoveAll(s => s.Id == id);
            await SaveHomePageContent(homeContent);
        }

        // Features Management
        public List<FeatureItemContentModel> GetAllFeatures()
        {
            var homeContent = GetHomePageContent();
            return homeContent.Features;
        }

        public async Task SaveFeature(FeatureItemContentModel model)
        {
            var homeContent = GetHomePageContent();
            
            if (model.Id == 0)
            {
                // Add new feature
                model.Id = homeContent.Features.Count > 0 ? homeContent.Features.Max(f => f.Id) + 1 : 1;
                homeContent.Features.Add(model);
            }
            else
            {
                // Update existing feature
                var existingIndex = homeContent.Features.FindIndex(f => f.Id == model.Id);
                if (existingIndex >= 0)
                {
                    homeContent.Features[existingIndex] = model;
                }
            }
            
            await SaveHomePageContent(homeContent);
        }

        public async Task DeleteFeature(int id)
        {
            var homeContent = GetHomePageContent();
            homeContent.Features.RemoveAll(f => f.Id == id);
            await SaveHomePageContent(homeContent);
        }

        // Service Categories Management
        public List<ServiceCategoryContentModel> GetAllServiceCategories()
        {
            var servicesContent = GetServicesPageContent();
            return servicesContent.ServiceCategories;
        }

        // Media Management
        public List<MediaFileModel> GetMediaFiles()
        {
            return new List<MediaFileModel>
            {
                new MediaFileModel
                {
                    Id = 1,
                    Name = "hero-image.jpg",
                    Url = "/images/hero-image.jpg",
                    Type = "image/jpeg",
                    Size = 1024 * 500, // 500KB
                    UploadedAt = DateTime.Now.AddDays(-5),
                    UploadedBy = "المدير",
                    Alt = "صورة البطل",
                    Description = "صورة البطل للصفحة الرئيسية"
                }
            };
        }

        public async Task<MediaFileModel> UploadMedia(IFormFile file)
        {
            var uploadsPath = Path.Combine("wwwroot", "uploads");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }

            var fileName = $"{Guid.NewGuid()}_{file.FileName}";
            var filePath = Path.Combine(uploadsPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            return new MediaFileModel
            {
                Id = new Random().Next(1000, 9999),
                Name = file.FileName,
                Url = $"/uploads/{fileName}",
                Type = file.ContentType,
                Size = file.Length,
                UploadedAt = DateTime.Now,
                UploadedBy = "المدير"
            };
        }

        // Site Settings
        public SiteSettingsModel GetSiteSettings()
        {
            var filePath = Path.Combine(_dataPath, "site-settings.json");
            
            if (File.Exists(filePath))
            {
                try
                {
                    var json = File.ReadAllText(filePath);
                    var settings = JsonSerializer.Deserialize<SiteSettingsModel>(json);
                    return settings ?? GetDefaultSiteSettings();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading site settings");
                }
            }
            
            return GetDefaultSiteSettings();
        }

        public async Task SaveSiteSettings(SiteSettingsModel model)
        {
            var filePath = Path.Combine(_dataPath, "site-settings.json");
            
            try
            {
                var json = JsonSerializer.Serialize(model, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("Site settings saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving site settings");
                throw;
            }
        }

        // Default Content Methods
        private HomePageContentModel GetDefaultHomeContent()
        {
            return new HomePageContentModel
            {
                Id = 1,
                TitleAr = "مدد بلس - منصة التمويل الجماعي المتوافقة مع الشريعة",
                TitleEn = "MDD Plus - Sharia-Compliant Crowdfunding Platform",
                DescriptionAr = "شركة سعودية مرخصة متخصصة في التمويل الجماعي",
                DescriptionEn = "Licensed Saudi company specializing in crowdfunding",
                HeroTitleAr = "منصة التمويل الجماعي المتوافقة مع الشريعة",
                HeroTitleEn = "Sharia-Compliant Crowdfunding Platform",
                HeroSubtitleAr = "نربط المستثمرين والمقترضين من خلال حلول مالية مبتكرة",
                HeroSubtitleEn = "Connecting investors and borrowers through innovative solutions",
                CtaTitleAr = "ابدأ رحلة الاستثمار الذكي اليوم",
                CtaTitleEn = "Start Your Smart Investment Journey Today",
                CtaSubtitleAr = "انضم إلى أكثر من 2500 مستثمر",
                CtaSubtitleEn = "Join over 2,500 investors",
                CtaButton1TextAr = "ابدأ الاستثمار الآن",
                CtaButton1TextEn = "Start Investing Now",
                CtaButton1Link = "/investors",
                CtaButton2TextAr = "تعرف على المنصة",
                CtaButton2TextEn = "Learn About Platform",
                CtaButton2Link = "/about",
                Statistics = new List<StatisticItemContentModel>
                {
                    new StatisticItemContentModel
                    {
                        Id = 1,
                        Number = "750",
                        Unit = "",
                        LabelAr = "مليون ريال",
                        LabelEn = "Million SAR",
                        DescriptionAr = "إجمالي التمويل المقدم",
                        DescriptionEn = "Total Funding Provided",
                        Color = "blue",
                        Icon = "💰",
                        AnimationDelay = 100,
                        ProgressPercentage = 75,
                        SortOrder = 1
                    }
                },
                Features = new List<FeatureItemContentModel>
                {
                    new FeatureItemContentModel
                    {
                        Id = 1,
                        TitleAr = "آمنة ومرخصة",
                        TitleEn = "Secure & Licensed",
                        DescriptionAr = "مرخصة من هيئة السوق المالية السعودية",
                        DescriptionEn = "Licensed by Saudi Capital Market Authority",
                        Icon = "🛡️",
                        Color = "blue",
                        IsHighlighted = true,
                        SortOrder = 1
                    }
                }
            };
        }

        private ServicesPageContentModel GetDefaultServicesContent()
        {
            return new ServicesPageContentModel
            {
                Id = 1,
                TitleAr = "خدماتنا المالية المتوافقة مع الشريعة",
                TitleEn = "Our Sharia-Compliant Financial Services",
                DescriptionAr = "نقدم مجموعة شاملة من الخدمات المالية المبتكرة",
                DescriptionEn = "We provide comprehensive innovative financial services",
                HeroTitleAr = "خدماتنا المالية المتوافقة مع الشريعة",
                HeroTitleEn = "Our Sharia-Compliant Financial Services",
                HeroSubtitleAr = "نقدم مجموعة شاملة من الخدمات المالية المبتكرة",
                HeroSubtitleEn = "We provide comprehensive innovative financial services",
                ServiceCategories = new List<ServiceCategoryContentModel>(),
                ProcessSteps = new List<ProcessStepContentModel>()
            };
        }

        private SiteSettingsModel GetDefaultSiteSettings()
        {
            return new SiteSettingsModel
            {
                SiteName = "مدد بلس",
                SiteDescription = "منصة التمويل الجماعي المتوافقة مع الشريعة",
                DefaultLanguage = "ar",
                EnableMultiLanguage = true,
                ContactEmail = "<EMAIL>",
                ContactPhone = "+966 11 123 4567",
                Address = "الرياض، المملكة العربية السعودية",
                EnableAnalytics = true,
                EnableCookieConsent = true,
                ThemeColor = "#005B82"
            };
        }
    }
}
