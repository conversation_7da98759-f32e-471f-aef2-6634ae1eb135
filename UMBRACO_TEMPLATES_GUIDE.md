# 🎨 **دليل Templates في Umbraco CMS - مدد بلس**

## 📋 **نظرة عامة**

Templates في Umbraco هي ملفات Razor (.cshtml) تحدد كيفية عرض المحتوى الذي ينشئه المحررون في الـ backoffice. كل Template مرتبط بـ Document Type ويستخدم Razor syntax لعرض البيانات.

## 🏗️ **هيكل Templates**

### **1. Master Template**
```
Views/Master.cshtml
```
**الغرض:** Template أساسي يحتوي على:
- HTML structure الأساسي
- Head section مع Meta tags
- Header والـ Navigation
- Footer
- Scripts الأساسية

**المميزات:**
- ✅ **Responsive Design** متكامل
- ✅ **SEO Optimization** مدمج
- ✅ **Multilingual Support** للعربية والإنجليزية
- ✅ **Dark/Light Mode** مع Theme Toggle
- ✅ **Accessibility Features** كاملة
- ✅ **PWA Ready** مع Manifest

### **2. Home Page Template**
```
Views/HomePageModel.cshtml
```
**الغرض:** Template للصفحة الرئيسية يحتوي على:
- Hero Section ديناميكي
- Statistics Section مع أنيميشن
- Features Section قابلة للتخصيص
- Call to Action Section

**البيانات المستخدمة:**
```csharp
@Model.TitleAr / @Model.TitleEn
@Model.DescriptionAr / @Model.DescriptionEn
@Model.HeroTitleAr / @Model.HeroTitleEn
@Model.HeroSubtitleAr / @Model.HeroSubtitleEn
@Model.Statistics (Nested Content)
@Model.Features (Nested Content)
@Model.CtaTitleAr / @Model.CtaTitleEn
@Model.CtaButton1TextAr / @Model.CtaButton1TextEn
@Model.CtaButton1Link
```

### **3. Services Page Template**
```
Views/ServicesPageModel.cshtml
```
**الغرض:** Template لصفحة الخدمات يحتوي على:
- Hero Section للخدمات
- أقسام المستثمرين والمقترضين
- Service Categories مع التفاصيل
- Process Steps التفاعلية

**البيانات المستخدمة:**
```csharp
@Model.TitleAr / @Model.TitleEn
@Model.HeroTitleAr / @Model.HeroTitleEn
@Model.InvestorsTitleAr / @Model.InvestorsTitleEn
@Model.BorrowersTitleAr / @Model.BorrowersTitleEn
@Model.ServiceCategories (Nested Content)
@Model.ProcessSteps (Nested Content)
```

## 🔧 **كيفية عمل Templates**

### **1. Template Inheritance**
```csharp
@{
    Layout = "Master.cshtml";  // يرث من Master Template
}
```

### **2. Model Binding**
```csharp
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<HomePageModel>
```

### **3. Language Support**
```csharp
var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
var isArabic = currentLanguage == "ar";
var isRtl = TranslationHelper.IsRTL(Context);

// استخدام البيانات حسب اللغة
@(isArabic ? Model.TitleAr : Model.TitleEn)
```

### **4. SEO Meta Tags**
```csharp
ViewBag.Title = isArabic ? Model.TitleAr : Model.TitleEn;
ViewBag.Description = isArabic ? Model.DescriptionAr : Model.DescriptionEn;
```

### **5. Sections**
```csharp
@section Head {
    <meta property="og:title" content="@ViewBag.Title">
}

@section Scripts {
    <script src="/js/page-specific.js"></script>
}
```

## 📊 **Nested Content في Templates**

### **Statistics Loop**
```csharp
@if (Model.Statistics != null && Model.Statistics.Any())
{
    @foreach (var stat in Model.Statistics.OrderBy(s => s.SortOrder))
    {
        <div class="stat-card">
            <span class="text-3xl">@stat.Icon</span>
            <div class="stat-number" data-target="@stat.Number">0</div>
            <h3>@(isArabic ? stat.LabelAr : stat.LabelEn)</h3>
            <p>@(isArabic ? stat.DescriptionAr : stat.DescriptionEn)</p>
        </div>
    }
}
```

### **Features Loop**
```csharp
@if (Model.Features != null && Model.Features.Any())
{
    @foreach (var feature in Model.Features.OrderBy(f => f.SortOrder))
    {
        <div class="feature-card @(feature.IsHighlighted ? "featured" : "")">
            <div class="w-16 h-16 bg-@(feature.Color)-500">
                <span>@feature.Icon</span>
            </div>
            <h3>@(isArabic ? feature.TitleAr : feature.TitleEn)</h3>
            <p>@(isArabic ? feature.DescriptionAr : feature.DescriptionEn)</p>
        </div>
    }
}
```

## 🎨 **CSS Classes المستخدمة**

### **Layout Classes**
- `hero-gradient` - خلفية متدرجة للـ Hero Section
- `section-padding` - مسافات موحدة للأقسام
- `container mx-auto` - حاوي مركزي متجاوب
- `card-advanced` - بطاقات متقدمة مع تأثيرات

### **Typography Classes**
- `font-arabic` - خط Cairo للعربية
- `font-english` - خط Inter للإنجليزية
- `text-right` / `text-left` - محاذاة النص حسب اللغة

### **Animation Classes**
- `animate-slide-bottom` - أنيميشن انزلاق من الأسفل
- `hover-lift` - تأثير رفع عند الحوم
- `animate-pulse` - تأثير نبض

### **Color Classes**
- `bg-mdd-primary` - اللون الأساسي لمدد بلس
- `text-mdd-secondary` - اللون الثانوي
- `border-mdd-accent` - لون الحدود

## 📱 **Responsive Design**

### **Breakpoints**
```css
/* Mobile First */
.grid-cols-1          /* Default: 1 column */
.md:grid-cols-2       /* Medium: 2 columns */
.lg:grid-cols-3       /* Large: 3 columns */
.xl:grid-cols-4       /* Extra Large: 4 columns */
```

### **Text Sizes**
```css
.text-xl              /* Mobile */
.lg:text-2xl          /* Desktop */
.text-4xl             /* Mobile Headlines */
.lg:text-6xl          /* Desktop Headlines */
```

### **Spacing**
```css
.px-4                 /* Mobile padding */
.sm:px-6              /* Small screens */
.lg:px-8              /* Large screens */
```

## 🔍 **SEO Features**

### **Meta Tags**
- Title tags ديناميكية
- Description tags محسنة
- Open Graph tags للشبكات الاجتماعية
- Twitter Card tags
- Canonical URLs

### **Structured Data**
```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "مدد بلس",
  "url": "https://mddplus.com"
}
</script>
```

### **Accessibility**
- Skip to content links
- ARIA labels
- Semantic HTML
- Keyboard navigation
- Screen reader support

## 🌐 **Multilingual Support**

### **Language Detection**
```csharp
var currentLanguage = TranslationHelper.GetCurrentLanguage(Context);
var isRtl = TranslationHelper.IsRTL(Context);
var isArabic = currentLanguage == "ar";
```

### **Content Direction**
```html
<html lang="@currentLanguage" dir="@(isRtl ? "rtl" : "ltr")">
<section dir="@(isRtl ? "rtl" : "ltr")">
```

### **Text Alignment**
```css
.text-@(isRtl ? "right" : "left")
.@(isRtl ? "mr-3" : "ml-3")
.space-x-reverse  /* للعربية */
```

## 🎭 **JavaScript Integration**

### **Counter Animation**
```javascript
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    counters.forEach(counter => {
        const target = parseInt(counter.dataset.target);
        // Animation logic
    });
}
```

### **Intersection Observer**
```javascript
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-slide-bottom');
        }
    });
});
```

### **Theme Toggle**
```javascript
document.getElementById('theme-toggle').addEventListener('click', () => {
    document.documentElement.classList.toggle('dark');
});
```

## 📁 **File Structure**

```
Views/
├── Master.cshtml                 # Master Template
├── HomePageModel.cshtml          # Home Page Template
├── ServicesPageModel.cshtml      # Services Page Template
└── Partials/                     # Partial Views
    ├── _Header.cshtml
    ├── _Footer.cshtml
    └── _Navigation.cshtml

uSync/v9/Templates/
├── Master.config                 # Master Template Config
├── HomePageModel.config          # Home Template Config
└── ServicesPageModel.config      # Services Template Config
```

## 🔧 **Template Configuration**

### **Master Template Config**
```xml
<Template Key="master-template-key" Alias="Master" Level="0">
  <Name>Master</Name>
  <Parent></Parent>
  <Path>/Views/Master.cshtml</Path>
</Template>
```

### **Child Template Config**
```xml
<Template Key="home-page-template-key" Alias="HomePageModel" Level="1">
  <Name>Home Page Model</Name>
  <Parent Key="master-template-key">Master</Parent>
  <Path>/Views/HomePageModel.cshtml</Path>
</Template>
```

## 🚀 **Best Practices**

### **1. Performance**
- استخدم Lazy Loading للصور
- ضغط CSS و JavaScript
- استخدم CDN للخطوط
- تحسين الصور

### **2. SEO**
- استخدم Semantic HTML
- أضف Alt text للصور
- استخدم Heading hierarchy صحيح
- أضف Schema markup

### **3. Accessibility**
- استخدم ARIA labels
- تأكد من Keyboard navigation
- استخدم ألوان متباينة
- أضف Skip links

### **4. Multilingual**
- استخدم Helper methods للترجمة
- تأكد من RTL support
- استخدم Unicode fonts
- اختبر على لغات مختلفة

## 🎯 **Testing Templates**

### **1. Responsive Testing**
```bash
# اختبار على أحجام مختلفة
- Mobile: 375px
- Tablet: 768px
- Desktop: 1200px
- Large: 1920px
```

### **2. Browser Testing**
- Chrome
- Firefox
- Safari
- Edge
- Mobile browsers

### **3. Performance Testing**
- PageSpeed Insights
- GTmetrix
- WebPageTest
- Lighthouse

## 📚 **Resources**

- [Umbraco Templates Documentation](https://docs.umbraco.com/umbraco-cms/fundamentals/design/templates)
- [Razor Syntax Reference](https://docs.microsoft.com/en-us/aspnet/core/mvc/views/razor)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

---

## 🎉 **النتيجة النهائية**

**Templates مدد بلس توفر:**

✅ **هيكل منظم ومرن**  
✅ **دعم متعدد اللغات كامل**  
✅ **تصميم متجاوب متقدم**  
✅ **تحسين SEO شامل**  
✅ **إمكانية وصول متكاملة**  
✅ **أداء محسن وسريع**  

**Templates جاهزة للاستخدام الاحترافي! 🚀**
