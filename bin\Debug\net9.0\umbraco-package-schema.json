{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"CollectionAliasConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.CollectionAlias\">"}, {"properties": {"match": {"description": "The collection that this extension should be available in", "type": "string"}}, "required": ["match"], "type": "object"}]}, "CollectionBulkActionPermissionConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.CollectionBulkActionPermission\">"}, {"properties": {"match": {"type": "object"}}, "required": ["match"], "type": "object"}]}, "ManifestAppEntryPoint": {"description": "Manifest for an `appEntryPoint`, which is loaded up front when the app starts.\n\nThis type of extension gives full control and will simply load the specified JS file.\nYou could have custom logic to decide which extensions to load/register by using extensionRegistry.\nThis is useful for extensions that need to be loaded up front, like an `authProvider`.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "appEntryPoint", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestAuthProvider": {"description": "Represents an authentication provider that can be used to authenticate users.\nThe provider needs to be registered in the API that the authorization request is sent to in order to be used.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forProviderName": {"description": "The provider name that this provider is for.", "examples": "'Umbraco.Github'", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaAuthProvider", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "authProvider", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forProviderName", "name", "type"], "type": "object"}, "ManifestBackofficeEntryPoint": {"description": "Manifest for an `backofficeEntryPoint`, which is loaded after the Backoffice has been loaded and authentication has been done.\n\nThis type of extension gives full control and will simply load the specified JS file.\nYou could have custom logic to decide which extensions to load/register by using extensionRegistry.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "backofficeEntryPoint", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestBase": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestBundle<UmbCoreManifestTypes>": {"description": "This type of extension takes a JS module and registers all exported manifests from the pointed JS file.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "bundle", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestCollection": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaCollection"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "collection", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestCollectionAction": {"description": "An action to perform on an entity\nFor example for content you may wish to create a new document etc", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaCollectionAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "collectionAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestCollectionActionCreateKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "create", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaCollectionAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "collectionAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestCollectionView": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaCollectionView", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "collectionView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestCondition": {"description": "This type of extension takes a JS module and registers all exported manifests from the pointed JS file.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "condition", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestCurrentUserAction<MetaCurrentUserAction>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaCurrentUserAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "currentUserAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestDashboard": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaDashboard", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "dashboard", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestDataSourceDataMapping<MetaDataSourceDataMapping>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "forDataModel": {"type": "string"}, "forDataSource": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaDataSourceDataMapping"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "dataSourceDataMapping", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forDataModel", "forDataSource", "meta", "name", "type"], "type": "object"}, "ManifestEntityAction<MetaEntityAction>": {"description": "An action to perform on an entity\nFor example for content you may wish to create a new document etc", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionCreateFolderKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "folderCreate", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionFolderKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionCreateKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "create", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionCreateKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionDefaultKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "default", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionDefaultKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionDeleteFolderKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "folderDelete", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionFolderKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionDeleteKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "delete", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionDeleteKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionDuplicateToKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "duplicateTo", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionDuplicateToKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionMoveToKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "moveTo", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionMoveToKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionReloadTreeItemChildrenKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "reloadTreeItemChildren", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionReloadTreeItemChildrenKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionSortChildrenOfContentKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "sortChildrenOfContent", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionSortChildrenOfKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionSortChildrenOfKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "sortChildrenOf", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionSortChildrenOfKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityActionUpdateFolderKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "folderUpdate", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityActionFolderKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkAction<MetaEntityBulkAction>": {"description": "An action to perform on multiple entities\nFor example for content you may wish to move one or more documents in bulk", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityBulkAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkActionDefaultKind<MetaEntityBulkActionDefaultKind>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "default", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityBulkActionDefaultKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkActionDeleteKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "delete", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityBulkActionDeleteKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkActionDuplicateToKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "duplicateTo", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityBulkActionDuplicateToKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkActionMoveToKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "moveTo", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityBulkActionMoveToKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityBulkActionTrashKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "trash", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaEntityBulkActionTrashKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "kind", "meta", "name", "type"], "type": "object"}, "ManifestEntityCreateOptionAction<MetaEntityCreateOptionAction>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityCreateOptionAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityCreateOptionAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "meta", "name", "type"], "type": "object"}, "ManifestEntityItemRef<MetaEntityItemRef>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityItemRef", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "entityItemRef", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "meta", "name", "type"], "type": "object"}, "ManifestEntityUserPermission": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityUserPermission"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "entityUserPermission", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "meta", "name", "type"], "type": "object"}, "ManifestEntryPoint": {"description": "Manifest for an `entryPoint`, which is loaded after the Backoffice has been loaded and authentication has been done.\n\nThis type of extension gives full control and will simply load the specified JS file.\nYou could have custom logic to decide which extensions to load/register by using extensionRegistry.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "entryPoint", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestExternalLoginProvider": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaExternalLoginProvider", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "externalLoginProvider", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestGlobalContext": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "globalContext", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestGlobalSearch": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaGlobalSearch"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "globalSearch", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestGranularUserPermission": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaGranularUserPermission", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "userGranularPermission", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestHeaderApp": {"description": "Header apps are displayed in the top right corner of the backoffice\nThe two provided header apps are the search and the user menu", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "headerApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestHeaderAppButtonKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "button", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaHeaderAppButtonKind", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "headerApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestIcons": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "icons", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestItemStore": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "itemStore", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestLocalization": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaLocalization"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "localization", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestMenu": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "menu", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestMenuItem": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaMenuItem", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "menuItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestMenuItemLinkKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "link", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaMenuItemLinkKind", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "menuItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestMenuItemTreeKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "tree", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaMenuItemTreeKind", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "menuItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestMfaLoginProvider": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forProviderName": {"description": "The provider names that this provider is for.", "examples": "'Umbraco.GoogleAuthenticator'", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaMfaLoginProvider", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "mfaL<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forProviderName", "name", "type"], "type": "object"}, "ManifestModal": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "modal", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestPickerSearchResultItem": {"description": "Represents a picker search result element.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "pickerSearchResultItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "name", "type"], "type": "object"}, "ManifestPreviewAppProvider": {"description": "Preview apps are displayed in the menu of the preview window.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "previewApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestPropertyContext<MetaPropertyContext>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "forPropertyEditorUis": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaPropertyContext"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "propertyContext", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forPropertyEditorUis", "meta", "name", "type"], "type": "object"}, "ManifestPropertyEditorSchema": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaPropertyEditorSchema"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyEditorSchema", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestPropertyEditorUi": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaPropertyEditorUi", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyEditorUi", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestPropertyValidationPathTranslator<any>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "forEditorAlias": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyValidationPathTranslator", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEditorAlias", "name", "type"], "type": "object"}, "ManifestPropertyValueCloner": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "forEditorAlias": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyValueCloner", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEditorAlias", "name", "type"], "type": "object"}, "ManifestPropertyValuePreset": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "forPropertyEditorSchemaAlias": {"type": "string"}, "forPropertyEditorUiAlias": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyValuePreset", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestPropertyValueResolver": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "forEditorAlias": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaPropertyValueResolver"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "propertyValueResolver", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEditorAlias", "name", "type"], "type": "object"}, "ManifestRepository<UmbApi>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "repository", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestSearchProvider": {"description": "Represents an search provider that can be used to search.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaSearchProvider"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "searchProvider", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestSearchResultItem": {"description": "Represents a search result element.", "properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "searchResultItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "name", "type"], "type": "object"}, "ManifestSection": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaSection", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "section", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestSectionRoute": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaSectionRoute"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "sectionRoute", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestSectionSidebarApp": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "sectionSidebarApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestSectionView": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaSectionView", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "sectionView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestStore": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "store", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestTree": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaTree"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "tree", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestTreeItem": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "treeItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forEntityTypes", "name", "type"], "type": "object"}, "ManifestTreeStore": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "treeStore", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestUfmComponent": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaUfmComponent"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "ufmComponent", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestUfmFilter": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaUfmFilter"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "ufmFilter", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestUserProfileApp": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaUserProfileApp", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "userProfileApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestWorkspace<MetaWorkspace,UmbControllerHostElement,UmbWorkspaceContext>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaWorkspace"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "workspace", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceAction<MetaWorkspaceAction,UmbWorkspaceAction<MetaWorkspaceAction>>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaWorkspaceAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceActionDefaultKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "default", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceActionDefaultKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceActionMenuItem<MetaWorkspaceActionMenuItem>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forWorkspaceActions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define which workspace actions this menu item should be shown for.", "examples": "[\r\n['Umb.WorkspaceAction.Document.Save', 'Umb.WorkspaceAction.Document.SaveAndPublish'],\r\n\"Umb.WorkspaceAction.Document.Save\"\r\n]"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaWorkspaceActionMenuItem"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceActionMenuItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forWorkspaceActions", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceActionMenuItemDefaultKind<MetaWorkspaceActionMenuItemDefaultKind>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "forWorkspaceActions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define which workspace actions this menu item should be shown for.", "examples": "[\r\n['Umb.WorkspaceAction.Document.Save', 'Umb.WorkspaceAction.Document.SaveAndPublish'],\r\n\"Umb.WorkspaceAction.Document.Save\"\r\n]"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "default", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceActionMenuItemDefaultKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceActionMenuItem", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "forWorkspaceActions", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceContext": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceContext", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestWorkspaceDefaultKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "default", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceDefaultKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "workspace", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceFooterApp": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceFooterApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "name", "type"], "type": "object"}, "ManifestWorkspaceFooterAppMenuBreadcrumbKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "menuBreadcrumb", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceFooterApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "name", "type"], "type": "object"}, "ManifestWorkspaceFooterAppVariantMenuBreadcrumbKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "variantMenuBreadcrumb", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceFooterApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "name", "type"], "type": "object"}, "ManifestWorkspaceInfoApp<MetaWorkspaceInfoApp>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaWorkspaceInfoApp", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceInfoApp", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceRoutableKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "routable", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceRoutableKind"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "type": {"const": "workspace", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceView<MetaWorkspaceView>": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaWorkspaceView", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceViewCollectionKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "collection", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceViewCollectionKind", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceViewContentCollectionKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "contentCollection", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceView", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "ManifestWorkspaceViewContentTypeDesignEditorKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"const": "contentTypeDesignEditor", "description": "The kind of the extension, used to group extensions together", "type": "string"}, "meta": {"$ref": "#/definitions/MetaWorkspaceViewContentTypeDesignEditorKind", "description": "This contains properties specific to the type of extension"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "type": {"const": "workspaceView", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "kind", "meta", "name", "type"], "type": "object"}, "MetaAuthProvider": {"properties": {"behavior": {"description": "The behavior of the provider when it is used.", "properties": {"autoRedirect": {"default": false, "description": "If true, the user will be redirected to the provider's login page immediately.", "type": "boolean"}, "popupFeatures": {"default": "'width=600,height=600,menubar=no,location=no,resizable=yes,scrollbars=yes,status=no,toolbar=no'", "description": "The features of the popup that is opened when the user logs in.", "type": "string"}, "popupTarget": {"default": "'umbracoAuthPopup'", "description": "The target of the popup that is opened when the user logs in.", "type": "string"}}, "type": "object"}, "defaultView": {"description": "The default view of the provider that is shown to the user.\nIf no element is provided, then the button will be rendered as a", "properties": {"color": {"default": "'secondary'", "description": "The color of the provider that is shown to the user.", "enum": ["", "danger", "default", "invalid", "positive", "warning"], "type": "string"}, "icon": {"default": "'icon-cloud'", "description": "The icon of the provider that is shown to the user.", "examples": "['icon-cloud', 'icon-github', 'icon-google', 'icon-facebook', 'icon-twitter', 'icon-x', 'icon-microsoft']", "type": "string"}, "look": {"default": "'default'", "description": "The look of the provider that is shown to the user.", "enum": ["", "default", "outline", "placeholder", "primary", "secondary"], "type": "string"}}, "type": "object"}, "label": {"description": "The label of the provider that is shown to the user.", "type": "string"}, "linking": {"description": "The linking options of the provider when it is used.", "properties": {"allowManualLinking": {"default": false, "description": "If true, the user will be able to link the provider to an existing account.", "type": "boolean"}}, "type": "object"}}, "type": "object"}, "MetaCollection": {"properties": {"noItemsLabel": {"type": "string"}, "repositoryAlias": {"type": "string"}}, "required": ["repositoryAlias"], "type": "object"}, "MetaCollectionAction": {"properties": {"additionalOptions": {"type": "boolean"}, "href": {"type": "string"}, "label": {"type": "string"}}, "required": ["label"], "type": "object"}, "MetaCollectionView": {"properties": {"icon": {"description": "An icon to represent the collection view", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the collection view", "type": "string"}, "pathName": {"description": "The URL pathname for this collection view that can be deep linked to by sharing the url", "type": "string"}}, "required": ["icon", "label", "pathName"], "type": "object"}, "MetaCurrentUserAction": {"type": "object"}, "MetaDashboard": {"properties": {"label": {"description": "The displayed name (label) in the navigation.", "type": "string"}, "pathname": {"description": "This is the URL path part for this view. This is used for navigating or deep linking directly to the dashboard\nhttps://yoursite.com/section/settings/dashboard/my-dashboard-path", "examples": ["my-dashboard-path"], "type": "string"}}, "type": "object"}, "MetaDataSourceDataMapping": {"type": "object"}, "MetaEntityAction": {"type": "object"}, "MetaEntityActionCreateKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["icon", "label"], "type": "object"}, "MetaEntityActionDefaultKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["icon", "label"], "type": "object"}, "MetaEntityActionDeleteKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "confirm": {"properties": {"headline": {"type": "string"}, "message": {"type": "string"}}, "type": "object"}, "detailRepositoryAlias": {"type": "string"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "itemRepositoryAlias": {"type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["detailRepositoryAlias", "icon", "itemRepositoryAlias", "label"], "type": "object"}, "MetaEntityActionDuplicateToKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "duplicateRepositoryAlias": {"type": "string"}, "foldersOnly": {"type": "boolean"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}, "treeAlias": {"type": "string"}, "treeRepositoryAlias": {"type": "string"}}, "required": ["duplicateRepositoryAlias", "icon", "label", "tree<PERSON><PERSON><PERSON>", "treeRepositoryAlias"], "type": "object"}, "MetaEntityActionFolderKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "folderRepositoryAlias": {"type": "string"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["folderRepositoryAlias", "icon", "label"], "type": "object"}, "MetaEntityActionMoveToKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "foldersOnly": {"type": "boolean"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}, "moveRepositoryAlias": {"type": "string"}, "treeAlias": {"type": "string"}, "treeRepositoryAlias": {"type": "string"}}, "required": ["icon", "label", "moveRepositoryAlias", "tree<PERSON><PERSON><PERSON>", "treeRepositoryAlias"], "type": "object"}, "MetaEntityActionReloadTreeItemChildrenKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["icon", "label"], "type": "object"}, "MetaEntityActionSortChildrenOfKind": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}, "sortChildrenOfRepositoryAlias": {"type": "string"}, "treeRepositoryAlias": {"type": "string"}}, "required": ["icon", "label", "sortChildrenOfRepositoryAlias", "treeRepositoryAlias"], "type": "object"}, "MetaEntityBulkAction": {"type": "object"}, "MetaEntityBulkActionDefaultKind": {"properties": {"icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["icon"], "type": "object"}, "MetaEntityBulkActionDeleteKind": {"properties": {"detailRepositoryAlias": {"type": "string"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "itemRepositoryAlias": {"type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["detailRepositoryAlias", "icon", "itemRepositoryAlias"], "type": "object"}, "MetaEntityBulkActionDuplicateToKind": {"properties": {"alias": {"description": "The alias of the extension, ensure it is unique", "type": "string"}, "api": {"description": "The file location of the api javascript file to load", "type": "string"}, "bulkDuplicateRepositoryAlias": {"type": "string"}, "conditions": {"description": "Set the conditions for when the extension should be loaded", "items": {"anyOf": [{"$ref": "#/definitions/UmbLanguageUserPermissionConditionConfig"}, {"$ref": "#/definitions/WorkspaceAliasConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityIsNewConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceEntityTypeConditionConfig"}, {"$ref": "#/definitions/UmbPropertyHasValueConditionConfig"}, {"$ref": "#/definitions/UmbWorkspaceContentTypeAliasConditionConfig"}, {"$ref": "#/definitions/UmbSectionUserPermissionConditionConfig"}, {"$ref": "#/definitions/SectionAliasConditionConfig"}, {"$ref": "#/definitions/CollectionBulkActionPermissionConditionConfig"}, {"$ref": "#/definitions/CollectionAliasConditionConfig"}, {"$ref": "#/definitions/UmbCurrentUserGroupIdConditionConfig"}, {"$ref": "#/definitions/UmbIsAdminConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">"}, {"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">"}, {"$ref": "#/definitions/SwitchConditionConfig"}, {"$ref": "#/definitions/UmbConditionConfigBase<string>"}]}, "type": "array"}, "element": {"description": "The file location of the element javascript file to load", "type": "string"}, "elementName": {"description": "The HTML web component name to use such as 'my-dashboard'\nNote it is NOT <my-dashboard></my-dashboard>, just the element name.", "type": "string"}, "foldersOnly": {"type": "boolean"}, "forEntityTypes": {"items": {"type": "string"}, "type": "array"}, "hideTreeRoot": {"type": "boolean"}, "js": {"description": "The file location of the javascript file to load", "type": "string"}, "kind": {"description": "The kind of the extension, used to group extensions together", "examples": ["button"]}, "meta": {"$ref": "#/definitions/MetaEntityBulkAction"}, "name": {"description": "The friendly name of the extension", "type": "string"}, "overwrites": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "Define one or more extension aliases that this extension should overwrite."}, "treeAlias": {"type": "string"}, "type": {"const": "entityBulkAction", "description": "The type of extension such as dashboard etc...", "type": "string"}, "weight": {"description": "Extensions such as dashboards are ordered by weight with lower numbers being first in the list", "type": "number"}}, "required": ["alias", "bulkDuplicateRepositoryAlias", "forEntityTypes", "meta", "name", "tree<PERSON><PERSON><PERSON>", "type"], "type": "object"}, "MetaEntityBulkActionMoveToKind": {"properties": {"bulkMoveRepositoryAlias": {"type": "string"}, "foldersOnly": {"type": "boolean"}, "hideTreeRoot": {"type": "boolean"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}, "treeAlias": {"type": "string"}}, "required": ["bulkMoveRepositoryAlias", "icon", "tree<PERSON><PERSON><PERSON>"], "type": "object"}, "MetaEntityBulkActionTrashKind": {"properties": {"bulkTrashRepositoryAlias": {"type": "string"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["bulkTrashRepositoryAlias", "icon"], "type": "object"}, "MetaEntityCreateOptionAction": {"properties": {"additionalOptions": {"description": "The action requires additional input from the user.\nA dialog will prompt the user for more information or to make a choice.", "type": "{boolean}"}, "description": {"description": "A description of the action to be performed", "examples": ["Create a document type with a template", "Create a document from a blueprint"], "type": "string"}, "icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create with Template", "Create from Blueprint"], "type": "string"}}, "required": ["icon", "label"], "type": "object"}, "MetaEntityItemRef": {"type": "object"}, "MetaEntityUserPermission": {"properties": {"description": {"type": "string"}, "group": {"type": "string"}, "label": {"type": "string"}, "verbs": {"items": {"type": "string"}, "type": "array"}}, "required": ["verbs"], "type": "object"}, "MetaExternalLoginProvider": {"properties": {"label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["label", "pathname"], "type": "object"}, "MetaGlobalSearch": {"properties": {"label": {"type": "string"}, "searchProviderAlias": {"type": "string"}}, "required": ["label", "searchProviderAlias"], "type": "object"}, "MetaGranularUserPermission": {"properties": {"description": {"type": "string"}, "descriptionKey": {"type": "string"}, "label": {"type": "string"}, "labelKey": {"type": "string"}, "schemaType": {"type": "string"}}, "required": ["schemaType"], "type": "object"}, "MetaHeaderAppButtonKind": {"properties": {"href": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}}, "required": ["href", "icon", "label"], "type": "object"}, "MetaLocalization": {"properties": {"culture": {"description": "The culture is a combination of a language and a country. The language is represented by an ISO 639-1 code and the country is represented by an ISO 3166-1 alpha-2 code.\r\nThe language and country are separated by a dash.\r\nThe value is used to describe the language of the translations according to the extension system\r\nand it will be set as the `lang` attribute on the `<html>` element.", "type": "string"}, "direction": {"default": "ltr", "description": "The value is used to describe the direction of the translations according to the extension system\r\nand it will be set as the `dir` attribute on the `<html>` element. It defaults to `ltr`.", "enum": ["ltr", "rtl"], "type": "string"}, "localizations": {"$ref": "#/definitions/UmbLocalizationDictionary", "description": "The localizations."}}, "required": ["culture"], "type": "object"}, "MetaMenuItem": {"properties": {"entityType": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "menus": {"items": {"type": "string"}, "type": "array"}}, "required": ["label", "menus"], "type": "object"}, "MetaMenuItemLinkKind": {"properties": {"entityType": {"type": "string"}, "href": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "menus": {"items": {"type": "string"}, "type": "array"}}, "required": ["href", "label", "menus"], "type": "object"}, "MetaMenuItemTreeKind": {"properties": {"entityType": {"type": "string"}, "hideTreeRoot": {"type": "boolean"}, "icon": {"type": "string"}, "label": {"type": "string"}, "menus": {"items": {"type": "string"}, "type": "array"}, "treeAlias": {"type": "string"}}, "required": ["label", "menus", "tree<PERSON><PERSON><PERSON>"], "type": "object"}, "MetaMfaLoginProvider": {"properties": {"label": {"type": "string"}}, "type": "object"}, "MetaPropertyContext": {"type": "object"}, "MetaPropertyEditorSchema": {"properties": {"defaultPropertyEditorUiAlias": {"type": "string"}, "settings": {"$ref": "#/definitions/PropertyEditorSettings"}}, "required": ["defaultPropertyEditorUiAlias"], "type": "object"}, "MetaPropertyEditorUi": {"properties": {"group": {"default": "Common", "description": "The group that this property editor UI belongs to, which will be used to group the property editor U<PERSON>s in the property editor picker.\nIf not specified, the property editor UI will be grouped under \"Common\".", "type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "propertyEditorSchemaAlias": {"description": "The alias of the property editor schema that this property editor UI is for.\nIf not specified, the property editor U<PERSON> can only be used to configure other property editors.", "type": "string"}, "settings": {"$ref": "#/definitions/PropertyEditorSettings"}, "supportsReadOnly": {"type": "boolean"}}, "required": ["group", "icon", "label"], "type": "object"}, "MetaPropertyValueResolver": {"properties": {"editorAlias": {"type": "string"}}, "type": "object"}, "MetaSearchProvider": {"properties": {"label": {"description": "The label of the provider that is shown to the user.", "type": "string"}}, "type": "object"}, "MetaSection": {"properties": {"label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["label", "pathname"], "type": "object"}, "MetaSectionRoute": {"properties": {"path": {"type": "string"}}, "type": "object"}, "MetaSectionView": {"properties": {"icon": {"description": "The icon displayed for this view in the navigation.", "examples": ["box"], "type": "string"}, "label": {"description": "The displayed name (label) in the navigation.", "type": "string"}, "pathname": {"description": "This is the URL path part for this view. This is used for navigating or deep linking directly to the view\nhttps://yoursite.com/section/settings/view/my-view-path", "examples": ["my-view-path"], "type": "string"}}, "required": ["icon"], "type": "object"}, "MetaTree": {"properties": {"repositoryAlias": {"type": "string"}}, "required": ["repositoryAlias"], "type": "object"}, "MetaUfmComponent": {"properties": {"alias": {"type": "string"}, "marker": {"type": "string"}}, "required": ["alias"], "type": "object"}, "MetaUfmFilter": {"properties": {"alias": {"type": "string"}}, "required": ["alias"], "type": "object"}, "MetaUserProfileApp": {"properties": {"label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["label", "pathname"], "type": "object"}, "MetaWorkspace": {"properties": {"entityType": {"type": "string"}}, "required": ["entityType"], "type": "object"}, "MetaWorkspaceAction": {"type": "object"}, "MetaWorkspaceActionDefaultKind": {"properties": {"additionalOptions": {"type": "boolean"}, "color": {"enum": ["", "danger", "default", "invalid", "positive", "warning"], "type": "string"}, "href": {"type": "string"}, "label": {"type": "string"}, "look": {"enum": ["", "default", "outline", "placeholder", "primary", "secondary"], "type": "string"}}, "type": "object"}, "MetaWorkspaceActionMenuItem": {"type": "object"}, "MetaWorkspaceActionMenuItemDefaultKind": {"properties": {"icon": {"description": "An icon to represent the action to be performed", "examples": ["icon-box", "icon-grid"], "type": "string"}, "label": {"description": "The friendly name of the action to perform", "examples": ["Create", "Create Content Template"], "type": "string"}}, "required": ["icon", "label"], "type": "object"}, "MetaWorkspaceDefaultKind": {"properties": {"entityType": {"type": "string"}, "headline": {"type": "string"}}, "required": ["entityType", "headline"], "type": "object"}, "MetaWorkspaceInfoApp": {"type": "object"}, "MetaWorkspaceRoutableKind": {"properties": {"entityType": {"type": "string"}}, "required": ["entityType"], "type": "object"}, "MetaWorkspaceView": {"properties": {"icon": {"type": "string"}, "label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["icon", "label", "pathname"], "type": "object"}, "MetaWorkspaceViewCollectionKind": {"properties": {"collectionAlias": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["collectionAlias", "icon", "label", "pathname"], "type": "object"}, "MetaWorkspaceViewContentTypeDesignEditorKind": {"properties": {"compositionRepositoryAlias": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "pathname": {"type": "string"}}, "required": ["icon", "label", "pathname"], "type": "object"}, "PropertyEditorSettings": {"properties": {"defaultData": {"items": {"$ref": "#/definitions/PropertyEditorSettingsDefaultData"}, "type": "array"}, "properties": {"items": {"$ref": "#/definitions/PropertyEditorSettingsProperty"}, "type": "array"}}, "required": ["properties"], "type": "object"}, "PropertyEditorSettingsDefaultData": {"properties": {"alias": {"type": "string"}, "value": {}}, "required": ["alias", "value"], "type": "object"}, "PropertyEditorSettingsProperty": {"properties": {"alias": {"type": "string"}, "config": {"items": {"properties": {"alias": {"type": "string"}, "value": {}}, "required": ["alias"], "type": "object"}, "type": "array"}, "description": {"type": "string"}, "label": {"type": "string"}, "propertyEditorUiAlias": {"type": "string"}, "weight": {"type": "number"}}, "required": ["alias", "label", "propertyEditorUiAlias"], "type": "object"}, "SectionAliasConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.SectionAlias\">"}, {"properties": {"match": {"description": "Define the section that this extension should be available in", "type": "string"}, "oneOf": {"description": "Define one or more workspaces that this extension should be available in", "items": {"type": "string"}, "type": "array"}}, "required": ["match"], "type": "object"}]}, "SwitchConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.Switch\">"}, {"properties": {"frequency": {"type": "string"}}, "required": ["frequency"], "type": "object"}]}, "UmbConditionConfigBase<\"Umb.Condition.CollectionAlias\">": {"properties": {"alias": {"const": "Umb.Condition.CollectionAlias", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.CollectionBulkActionPermission\">": {"properties": {"alias": {"const": "Umb.Condition.CollectionBulkActionPermission", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.SectionAlias\">": {"properties": {"alias": {"const": "Umb.Condition.SectionAlias", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.SectionUserPermission\">": {"properties": {"alias": {"const": "Umb.Condition.SectionUserPermission", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.Switch\">": {"properties": {"alias": {"const": "Umb.Condition.Switch", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowChangePassword\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowChangePassword", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowDeleteAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowDeleteAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowDisableAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowDisableAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowEnableAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowEnableAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowExternalLoginAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowExternalLoginAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowMfaAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowMfaAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.User.AllowUnlockAction\">": {"properties": {"alias": {"const": "Umb.Condition.User.AllowUnlockAction", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.UserPermission.Language\">": {"properties": {"alias": {"const": "Umb.Condition.UserPermission.Language", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.WorkspaceContentTypeAlias\">": {"properties": {"alias": {"const": "Umb.Condition.WorkspaceContentTypeAlias", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<\"Umb.Condition.WorkspaceEntityType\">": {"properties": {"alias": {"const": "Umb.Condition.WorkspaceEntityType", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbConditionConfigBase<string>": {"properties": {"alias": {"type": "string"}}, "required": ["alias"], "type": "object"}, "UmbCurrentUserGroupIdConditionConfig": {"properties": {"alias": {"const": "Umb.Condition.CurrentUser.GroupId", "type": "string"}, "allOf": {"description": "The user groups that the current user must be a member of to pass the condition.", "examples": "[['guid1', 'guid2']]", "items": {"type": "string"}, "type": "array"}, "match": {"description": "The user group that the current user must be a member of to pass the condition.", "examples": "['guid1']", "type": "string"}, "noneOf": {"description": "The user group(s) that the current user must not be a member of to pass the condition.", "examples": "[['guid1', 'guid2']]", "items": {"type": "string"}, "type": "array"}, "oneOf": {"description": "The user group(s) that the current user must be a member of to pass the condition.", "examples": "[['guid1', 'guid2']]", "items": {"type": "string"}, "type": "array"}}, "required": ["alias"], "type": "object"}, "UmbIsAdminConditionConfig": {"properties": {"alias": {"const": "Umb.Condition.CurrentUser.IsAdmin", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbLanguageUserPermissionConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.UserPermission.Language\">"}, {"properties": {"allOf": {"description": "The user must have all of the permissions in this array for the condition to be met.", "items": {"type": "string"}, "type": "array"}, "match": {"type": "string"}, "oneOf": {"description": "The user must have at least one of the permissions in this array for the condition to be met.", "items": {"type": "string"}, "type": "array"}}, "required": ["match"], "type": "object"}]}, "UmbLocalizationDictionary": {"type": "object"}, "UmbPropertyHasValueConditionConfig": {"properties": {"alias": {"const": "Umb.Condition.Property.HasValue", "type": "string"}}, "required": ["alias"], "type": "object"}, "UmbSectionUserPermissionConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.SectionUserPermission\">"}, {"properties": {"match": {"type": "string"}}, "required": ["match"], "type": "object"}]}, "UmbWorkspaceContentTypeAliasConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.WorkspaceContentTypeAlias\">"}, {"properties": {"match": {"description": "Define a content type alias in which workspace this extension should be available", "type": "string"}, "oneOf": {"description": "Define one or more content type aliases in which workspace this extension should be available", "items": {"type": "string"}, "type": "array"}}, "type": "object"}]}, "UmbWorkspaceEntityIsNewConditionConfig": {"properties": {"alias": {"const": "Umb.Condition.WorkspaceEntityIsNew", "type": "string"}, "match": {"type": "boolean"}}, "required": ["alias", "match"], "type": "object"}, "UmbWorkspaceEntityTypeConditionConfig": {"allOf": [{"$ref": "#/definitions/UmbConditionConfigBase<\"Umb.Condition.WorkspaceEntityType\">"}, {"properties": {"match": {"description": "Define the workspace that this extension should be available in", "type": "string"}}, "required": ["match"], "type": "object"}]}, "UmbracoPackageImportmap": {"properties": {"imports": {"$ref": "#/definitions/UmbracoPackageImportmapValue", "description": "This is used to define the module specifiers and their respective paths for the package to be used in the browser.", "examples": [{"library": "./path/to/library.js", "library/*": "./path/to/library/*"}], "title": "A module specifier with a path for the importmap"}, "scopes": {"$ref": "#/definitions/UmbracoPackageImportmapScopes", "description": "This is used to define the scopes for the package to be used in the browser. It has to specify a path and a value that is an object with module specifiers and their respective paths.", "examples": [{"/path/to/library": {"library": "./path/to/some/other/library.js"}}], "title": "The importmap scopes for the package"}}, "required": ["imports"], "type": "object"}, "UmbracoPackageImportmapScopes": {"additionalProperties": {"$ref": "#/definitions/UmbracoPackageImportmapValue"}, "type": "object"}, "UmbracoPackageImportmapValue": {"additionalProperties": {"type": "string"}, "type": "object"}, "WorkspaceAliasConditionConfig": {"properties": {"alias": {"const": "Umb.Condition.WorkspaceAlias", "type": "string"}, "match": {"description": "Define the workspace that this extension should be available in", "type": "string"}, "oneOf": {"description": "Define one or more workspaces that this extension should be available in", "items": {"type": "string"}, "type": "array"}}, "required": ["alias"], "type": "object"}}, "description": "Umbraco package manifest JSON", "properties": {"allowPackageTelemetry": {"default": true, "title": "Decides if the package sends telemetry data for collection", "type": "boolean"}, "allowPublicAccess": {"default": false, "title": "Decides if the package is allowed to be accessed by the public, e.g. on the login screen", "type": "boolean"}, "allowTelemetry": {"default": true, "title": "Decides if the package sends telemetry data for collection", "type": "boolean"}, "extensions": {"items": {"anyOf": [{"$ref": "#/definitions/ManifestBase"}, {"$ref": "#/definitions/ManifestCondition"}, {"$ref": "#/definitions/ManifestAuthProvider"}, {"$ref": "#/definitions/ManifestModal"}, {"$ref": "#/definitions/ManifestWorkspaceDefaultKind"}, {"$ref": "#/definitions/ManifestWorkspaceActionDefaultKind"}, {"$ref": "#/definitions/ManifestWorkspaceFooterApp"}, {"$ref": "#/definitions/ManifestWorkspaceFooterAppMenuBreadcrumbKind"}, {"$ref": "#/definitions/ManifestWorkspaceFooterAppVariantMenuBreadcrumbKind"}, {"$ref": "#/definitions/ManifestWorkspaceViewContentTypeDesignEditorKind"}, {"$ref": "#/definitions/ManifestWorkspaceView<MetaWorkspaceView>"}, {"$ref": "#/definitions/ManifestWorkspaceRoutableKind"}, {"$ref": "#/definitions/ManifestWorkspaceContext"}, {"$ref": "#/definitions/ManifestPropertyV<PERSON>ue<PERSON>loner"}, {"$ref": "#/definitions/ManifestPropertyEditorUi"}, {"$ref": "#/definitions/ManifestPropertyEditorSchema"}, {"$ref": "#/definitions/ManifestPropertyValuePreset"}, {"$ref": "#/definitions/ManifestPropertyValueResolver"}, {"$ref": "#/definitions/ManifestUfmComponent"}, {"$ref": "#/definitions/ManifestUfmFilter"}, {"$ref": "#/definitions/ManifestEntityActionDeleteKind"}, {"$ref": "#/definitions/ManifestEntityActionDefaultKind"}, {"$ref": "#/definitions/ManifestEntityActionCreateKind"}, {"$ref": "#/definitions/ManifestIcons"}, {"$ref": "#/definitions/ManifestEntityActionDuplicateToKind"}, {"$ref": "#/definitions/ManifestEntityActionMoveToKind"}, {"$ref": "#/definitions/ManifestEntityActionReloadTreeItemChildrenKind"}, {"$ref": "#/definitions/ManifestEntityActionSortChildrenOfKind"}, {"$ref": "#/definitions/ManifestTreeItem"}, {"$ref": "#/definitions/ManifestSectionRoute"}, {"$ref": "#/definitions/ManifestSectionSidebarApp"}, {"$ref": "#/definitions/ManifestSectionView"}, {"$ref": "#/definitions/ManifestSection"}, {"$ref": "#/definitions/ManifestDashboard"}, {"$ref": "#/definitions/ManifestMenu"}, {"$ref": "#/definitions/ManifestTree"}, {"$ref": "#/definitions/ManifestEntityActionCreateFolderKind"}, {"$ref": "#/definitions/ManifestEntityActionDeleteFolderKind"}, {"$ref": "#/definitions/ManifestEntityActionUpdateFolderKind"}, {"$ref": "#/definitions/ManifestMenuItem"}, {"$ref": "#/definitions/ManifestMenuItemLinkKind"}, {"$ref": "#/definitions/ManifestMenuItemTreeKind"}, {"$ref": "#/definitions/ManifestWorkspaceViewContentCollectionKind"}, {"$ref": "#/definitions/ManifestEntityActionSortChildrenOfContentKind"}, {"$ref": "#/definitions/ManifestCollectionAction"}, {"$ref": "#/definitions/ManifestCollectionView"}, {"$ref": "#/definitions/ManifestCollection"}, {"$ref": "#/definitions/ManifestCollectionActionCreateKind"}, {"$ref": "#/definitions/ManifestWorkspaceViewCollectionKind"}, {"$ref": "#/definitions/ManifestSearchProvider"}, {"$ref": "#/definitions/ManifestSearchResultItem"}, {"$ref": "#/definitions/ManifestGlobalSearch"}, {"$ref": "#/definitions/ManifestPickerSearchResultItem"}, {"$ref": "#/definitions/ManifestUserProfileApp"}, {"$ref": "#/definitions/ManifestEntityUserPermission"}, {"$ref": "#/definitions/ManifestGranularUserPermission"}, {"$ref": "#/definitions/ManifestLocalization"}, {"$ref": "#/definitions/ManifestAppEntryPoint"}, {"$ref": "#/definitions/ManifestBackofficeEntryPoint"}, {"$ref": "#/definitions/ManifestEntryPoint"}, {"$ref": "#/definitions/ManifestBundle<UmbCoreManifestTypes>"}, {"$ref": "#/definitions/ManifestWorkspaceActionMenuItem<MetaWorkspaceActionMenuItem>"}, {"$ref": "#/definitions/ManifestWorkspaceActionMenuItemDefaultKind<MetaWorkspaceActionMenuItemDefaultKind>"}, {"$ref": "#/definitions/ManifestWorkspaceInfoApp<MetaWorkspaceInfoApp>"}, {"$ref": "#/definitions/ManifestWorkspaceAction<MetaWorkspaceAction,UmbWorkspaceAction<MetaWorkspaceAction>>"}, {"$ref": "#/definitions/ManifestWorkspace<MetaWorkspace,UmbControllerHostElement,UmbWorkspaceContext>"}, {"$ref": "#/definitions/ManifestPropertyContext<MetaPropertyContext>"}, {"$ref": "#/definitions/ManifestDataSourceDataMapping<MetaDataSourceDataMapping>"}, {"$ref": "#/definitions/ManifestEntityAction<MetaEntityAction>"}, {"$ref": "#/definitions/ManifestEntityCreateOptionAction<MetaEntityCreateOptionAction>"}, {"$ref": "#/definitions/ManifestEntityItemRef<MetaEntityItemRef>"}, {"$ref": "#/definitions/ManifestPropertyValidationPathTranslator<any>"}, {"$ref": "#/definitions/ManifestCurrentUserAction<MetaCurrentUserAction>"}, {"$ref": "#/definitions/ManifestEntityBulkActionDeleteKind"}, {"$ref": "#/definitions/ManifestEntityBulkAction<MetaEntityBulkAction>"}, {"$ref": "#/definitions/ManifestEntityBulkActionDefaultKind<MetaEntityBulkActionDefaultKind>"}, {"$ref": "#/definitions/ManifestEntityBulkActionDuplicateToKind"}, {"$ref": "#/definitions/ManifestEntityBulkActionMoveToKind"}, {"$ref": "#/definitions/ManifestEntityBulkActionTrashKind"}, {"$ref": "#/definitions/ManifestExternalLoginProvider"}, {"$ref": "#/definitions/ManifestGlobalContext"}, {"$ref": "#/definitions/ManifestHeaderApp"}, {"$ref": "#/definitions/ManifestHeaderAppButtonKind"}, {"$ref": "#/definitions/ManifestMfaLoginProvider"}, {"$ref": "#/definitions/ManifestPreviewAppProvider"}, {"$ref": "#/definitions/ManifestRepository<UmbApi>"}, {"$ref": "#/definitions/ManifestStore"}, {"$ref": "#/definitions/ManifestTreeStore"}, {"$ref": "#/definitions/ManifestItemStore"}]}, "title": "An array of Umbraco package manifest types that will be installed", "type": "array"}, "id": {"title": "The unique identifier of the Umbraco package", "type": "string"}, "importmap": {"$ref": "#/definitions/UmbracoPackageImportmap", "description": "This is used to define the imports and the scopes for the package to be used in the browser. It will be combined with the global importmap.", "title": "The importmap for the package"}, "name": {"title": "The name of the Umbraco package", "type": "string"}, "version": {"examples": ["0.1.0"], "title": "The version of the Umbraco package in the style of semver", "type": "string"}}, "required": ["extensions", "name"], "type": "object"}