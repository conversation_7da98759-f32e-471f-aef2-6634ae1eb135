using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MddPlus.Models.DocumentTypes
{
    public class BasePageModel : PublishedContentModel
    {
        public BasePageModel(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // SEO Properties
        public virtual string MetaTitle => this.Value<string>("metaTitle") ?? Name;
        public virtual string MetaDescription => this.Value<string>("metaDescription") ?? string.Empty;
        public virtual string MetaKeywords => this.Value<string>("metaKeywords") ?? string.Empty;
        public virtual IPublishedContent MetaImage => this.Value<IPublishedContent>("metaImage");

        // Multilingual Properties
        public virtual string TitleAr => this.Value<string>("titleAr") ?? Name;
        public virtual string TitleEn => this.Value<string>("titleEn") ?? Name;
        public virtual string DescriptionAr => this.Value<string>("descriptionAr") ?? string.Empty;
        public virtual string DescriptionEn => this.Value<string>("descriptionEn") ?? string.Empty;

        // Navigation
        public virtual bool HideFromNavigation => this.Value<bool>("hideFromNavigation");
        public virtual int NavigationOrder => this.Value<int>("navigationOrder");

        // Theme & Layout
        public virtual string PageTheme => this.Value<string>("pageTheme") ?? "default";
        public virtual string LayoutType => this.Value<string>("layoutType") ?? "standard";

        // Helper Methods
        public string GetTitle(string language = "ar")
        {
            return language == "ar" ? TitleAr : TitleEn;
        }

        public string GetDescription(string language = "ar")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }
    }
}
