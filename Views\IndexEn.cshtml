@{
    Layout = "_Layout.cshtml";
    ViewBag.Title = "MDD Plus - Sharia-Compliant Crowdfunding Platform";
    ViewBag.Description = "Licensed Saudi company specializing in Sharia-compliant debt-based crowdfunding. Connecting investors and borrowers through reliable financial solutions.";
}

@section Head {
    <meta property="og:title" content="@ViewBag.Title">
    <meta property="og:description" content="@ViewBag.Description">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="en_US">
}

<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden section-padding" dir="ltr">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative container mx-auto">
        <div class="text-center text-white max-w-4xl mx-auto stagger-animation">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
                <span class="text-sm font-medium">🇸🇦 Supporting Saudi Vision 2030</span>
            </div>
            
            <h1 class="text-4xl lg:text-6xl font-bold mb-6 text-gradient">
                Sharia-Compliant Crowdfunding Platform
            </h1>
            <p class="text-xl lg:text-2xl text-gray-200 leading-relaxed mb-8">
                Connecting investors and borrowers through innovative financial solutions compliant with Islamic Sharia, contributing to Saudi Vision 2030
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <a href="/investors" class="btn-primary btn-advanced text-lg px-8 py-4 hover-lift">
                    💰 Start Investing
                </a>
                <a href="/borrowers" class="btn-secondary text-lg px-8 py-4 hover-lift">
                    🏦 Get Funding
                </a>
            </div>
            
            <div class="flex flex-wrap justify-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>Licensed by CMA</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>Sharia Compliant</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>100% Secure & Guaranteed</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="section-padding bg-white dark:bg-gray-900" dir="ltr">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                Numbers That Speak for Themselves
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Outstanding results and attractive returns for our investors
            </p>
        </div>
        
        <div class="grid-responsive">
            <div class="card-advanced text-center hover-glow">
                <div class="text-4xl lg:text-5xl font-bold text-blue-600 dark:text-blue-400 mb-2 stat-number">750</div>
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">Million SAR</div>
                <div class="text-gray-500 dark:text-gray-400">Total Funding Provided</div>
                <div class="progress-advanced mt-4">
                    <div class="progress-bar" style="width: 75%"></div>
                </div>
            </div>
            
            <div class="card-advanced text-center hover-glow">
                <div class="text-4xl lg:text-5xl font-bold text-green-600 dark:text-green-400 mb-2 stat-number">2,500</div>
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">Investors</div>
                <div class="text-gray-500 dark:text-gray-400">Active Investors on Platform</div>
                <div class="progress-advanced mt-4">
                    <div class="progress-bar" style="width: 85%"></div>
                </div>
            </div>
            
            <div class="card-advanced text-center hover-glow">
                <div class="text-4xl lg:text-5xl font-bold text-purple-600 dark:text-purple-400 mb-2 stat-number">98%</div>
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">Success Rate</div>
                <div class="text-gray-500 dark:text-gray-400">Project Success Rate</div>
                <div class="progress-advanced mt-4">
                    <div class="progress-bar" style="width: 98%"></div>
                </div>
            </div>
            
            <div class="card-advanced text-center hover-glow">
                <div class="text-4xl lg:text-5xl font-bold text-orange-600 dark:text-orange-400 mb-2 stat-number">15.8%</div>
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">Annual Return</div>
                <div class="text-gray-500 dark:text-gray-400">Average Annual Return</div>
                <div class="progress-advanced mt-4">
                    <div class="progress-bar" style="width: 90%"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="section-padding bg-gray-50 dark:bg-gray-800" dir="ltr">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6 text-gray-900 dark:text-white">
                Why Choose MDD Plus?
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                We provide a secure and trusted platform for Sharia-compliant crowdfunding, with strong guarantees and attractive returns
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">🛡️</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Secure & Licensed</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Licensed by Saudi Capital Market Authority with highest security, protection and transparency standards
                </p>
            </div>
            
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">☪️</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Sharia Compliant</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    All products and services comply with Islamic Sharia principles and are certified by the Sharia Board
                </p>
            </div>
            
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">📈</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Attractive Returns</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Competitive returns up to 15.8% annually with strong guarantees and calculated risks
                </p>
            </div>
            
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">🔍</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Full Transparency</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Complete transparency in all operations and investments with regular reports and direct monitoring
                </p>
            </div>
            
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">🎯</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Expert Support</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Specialized team of financial experts to support you in your investment journey 24/7
                </p>
            </div>
            
            <div class="card-advanced text-center feature-item hover-lift">
                <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 feature-icon">
                    <span class="text-2xl">📱</span>
                </div>
                <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">User-Friendly Platform</h3>
                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Easy-to-use digital platform with advanced mobile app and fully responsive interface
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding hero-gradient relative overflow-hidden" dir="ltr">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative container mx-auto text-center">
        <div class="max-w-4xl mx-auto text-white">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                Start Your Smart Investment Journey Today
            </h2>
            <p class="text-xl mb-8 text-gray-200">
                Join over 2,500 investors achieving attractive returns up to 15.8% annually through our Sharia-compliant platform
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/investors" class="btn-primary btn-advanced text-lg px-10 py-4 bg-white text-mdd-primary hover:bg-gray-100 shadow-2xl hover-lift">
                    💰 Start Investing Now
                </a>
                <a href="/about" class="btn-secondary text-lg px-10 py-4 border-2 border-white text-white hover:bg-white hover:text-mdd-primary hover-lift">
                    📖 Learn About Platform
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Floating Action Button -->
<button class="btn-floating no-print" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" data-tooltip="Back to top">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

@section Scripts {
    <script>
        // English page specific scripts
        document.addEventListener('DOMContentLoaded', function() {
            // Show welcome notification for English users
            setTimeout(() => {
                if (window.advancedFeatures) {
                    window.advancedFeatures.showNotification(
                        'Welcome to MDD Plus! Switch to Arabic for the full experience.',
                        'info',
                        8000
                    );
                }
            }, 2000);
            
            // Enhanced animations for English layout
            const cards = document.querySelectorAll('.feature-item');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
}
